import request from './request'

// 获取积分记录列表
export function getPointRecords(params) {
  return request({
    url: '/admin/point-records',
    method: 'get',
    params
  })
}

// 获取积分统计信息
export function getPointStats() {
  return request({
    url: '/admin/points/stats',
    method: 'get'
  })
}

// 手动调整用户积分
export function adjustUserPoints(data) {
  return request({
    url: '/admin/points/adjust',
    method: 'post',
    data
  })
}

// 批量调整积分
export function batchAdjustPoints(data) {
  return request({
    url: '/admin/points/batch-adjust',
    method: 'post',
    data
  })
}

// 获取积分配置
export function getPointsConfig() {
  return request({
    url: '/admin/points/config',
    method: 'get'
  })
}

// 更新积分配置
export function updatePointsConfig(data) {
  return request({
    url: '/admin/points/config',
    method: 'put',
    data
  })
}

// 导出积分记录
export function exportPointRecords(params) {
  return request({
    url: '/admin/point-records/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
