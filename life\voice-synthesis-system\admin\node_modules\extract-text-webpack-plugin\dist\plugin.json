{"type": "object", "additionalProperties": false, "properties": {"allChunks": {"description": "", "type": "boolean"}, "disable": {"description": "", "type": "boolean"}, "fallback": {"description": "A loader that webpack can fall back to if the original one fails.", "modes": {"type": ["string", "object", "array"]}}, "filename": {"description": "The filename and path that ExtractTextPlugin will extract to", "modes": {"type": ["string", "function"]}}, "ignoreOrder": {"description": "Ignore dependency order (useful for CSS Modules)", "type": "boolean"}, "loader": {"description": "The loader that ExtractTextPlugin will attempt to load through.", "modes": {"type": ["string", "object", "array"]}}, "publicPath": {"description": "", "type": "string"}}}