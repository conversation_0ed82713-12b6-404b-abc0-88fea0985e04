package com.voice.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voice.entity.SynthesisRecord;
import com.voice.mapper.SynthesisRecordMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 语音合成记录服务类
 */
@Service
public class SynthesisRecordService extends ServiceImpl<SynthesisRecordMapper, SynthesisRecord> {

    /**
     * 分页查询用户合成记录
     */
    public IPage<SynthesisRecord> getUserSynthesisRecords(Long userId, int current, int size) {
        Page<SynthesisRecord> page = new Page<>(current, size);
        QueryWrapper<SynthesisRecord> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        wrapper.orderByDesc("create_time");
        
        return this.page(page, wrapper);
    }

    /**
     * 分页查询所有合成记录
     */
    public IPage<SynthesisRecord> getSynthesisRecordPage(int current, int size, Long userId, Integer status) {
        Page<SynthesisRecord> page = new Page<>(current, size);
        QueryWrapper<SynthesisRecord> wrapper = new QueryWrapper<>();

        if (userId != null) {
            wrapper.eq("user_id", userId);
        }

        if (status != null) {
            wrapper.eq("status", status);
        }

        wrapper.orderByDesc("create_time");
        return this.page(page, wrapper);
    }

    /**
     * 获取用户合成次数统计
     */
    public int getUserSynthesisCount(Long userId) {
        QueryWrapper<SynthesisRecord> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        wrapper.eq("status", 1); // 只统计成功的合成记录

        return Math.toIntExact(this.count(wrapper));
    }

    /**
     * 获取合成统计信息
     */
    public Map<String, Object> getSynthesisStats() {
        Map<String, Object> stats = new HashMap<>();

        // 总合成次数
        long totalSynthesis = this.count();
        stats.put("totalSynthesis", totalSynthesis);

        // 成功合成次数
        QueryWrapper<SynthesisRecord> successWrapper = new QueryWrapper<>();
        successWrapper.eq("status", 1);
        long successSynthesis = this.count(successWrapper);
        stats.put("successSynthesis", successSynthesis);

        // 失败合成次数
        QueryWrapper<SynthesisRecord> failWrapper = new QueryWrapper<>();
        failWrapper.eq("status", 0);
        long failSynthesis = this.count(failWrapper);
        stats.put("failSynthesis", failSynthesis);

        // 今日合成次数
        QueryWrapper<SynthesisRecord> todayWrapper = new QueryWrapper<>();
        todayWrapper.ge("create_time", LocalDateTime.now().toLocalDate());
        long todaySynthesis = this.count(todayWrapper);
        stats.put("todaySynthesis", todaySynthesis);

        return stats;
    }

    /**
     * 获取语音模型列表
     */
    public List<Map<String, Object>> getVoiceModels() {
        List<Map<String, Object>> models = new ArrayList<>();

        // 这里应该从数据库或配置文件中获取模型列表
        // 暂时返回模拟数据
        Map<String, Object> model1 = new HashMap<>();
        model1.put("id", 1);
        model1.put("name", "zhexue_lao");
        model1.put("displayName", "哲学老师");
        model1.put("status", 1);
        models.add(model1);

        Map<String, Object> model2 = new HashMap<>();
        model2.put("id", 2);
        model2.put("name", "xiaoshuo_nan");
        model2.put("displayName", "小说男声");
        model2.put("status", 1);
        models.add(model2);

        Map<String, Object> model3 = new HashMap<>();
        model3.put("id", 3);
        model3.put("name", "default");
        model3.put("displayName", "默认模型");
        model3.put("status", 1);
        models.add(model3);

        return models;
    }
}
