package com.voice.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * 异步配置类
 * 用于处理长时间运行的语音合成任务
 */
@Configuration
@EnableAsync
public class AsyncConfig {

    @Bean(name = "voiceSynthesisExecutor")
    public Executor voiceSynthesisExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(2);
        // 最大线程数
        executor.setMaxPoolSize(5);
        // 队列容量
        executor.setQueueCapacity(100);
        // 线程名前缀
        executor.setThreadNamePrefix("VoiceSynthesis-");
        // 设置线程空闲时间，超过该时间线程会被回收
        executor.setKeepAliveSeconds(300);
        // 设置拒绝策略：当pool已经达到max size的时候，如何处理新任务
        // CALLER_RUNS：不在新线程中执行任务，而是有调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 等待时间 (默认为0，此时立即停止)，并没等待xx秒后强制停止
        executor.setAwaitTerminationSeconds(7200); // 2小时
        executor.initialize();
        return executor;
    }
}
