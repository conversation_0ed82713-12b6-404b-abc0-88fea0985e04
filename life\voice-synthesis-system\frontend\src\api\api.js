/**
 * API统一导出
 * 将所有API模块统一导出，方便在组件中使用
 */

// 导入各个模块的API
import * as auth from './auth'
import * as voice from './voice'
import * as user from './user'

// 统一导出
export default {
  // 认证相关
  auth: {
    login: auth.login,
    register: auth.register,
    getUserInfo: auth.getUserInfo,
    refreshToken: auth.refreshToken,
    logout: auth.logout
  },

  // 语音合成相关
  voice: {
    getModels: voice.getVoiceModels,
    synthesize: voice.synthesizeVoice,
    getRecords: voice.getSynthesisRecords,
    deleteRecord: voice.deleteSynthesisRecord,
    batchDeleteRecords: voice.batchDeleteSynthesisRecords,
    downloadAudio: voice.downloadAudio
  },

  // 用户相关
  user: {
    getStats: user.getUserStats,
    updateProfile: user.updateUserProfile,
    changePassword: user.changePassword,
    getPointRecords: user.getPointRecords,
    getPoints: user.getUserPoints,
    rechargePoints: user.rechargePoints,
    uploadAvatar: user.uploadAvatar
  }
}

// 也可以分别导出，供按需导入使用
export { auth, voice, user }
