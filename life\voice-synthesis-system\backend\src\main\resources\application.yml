server:
  port: 8080
  servlet:
    context-path: /api
  tomcat:
    connection-timeout: 7200000  # 2小时
    keep-alive-timeout: 7200000  # 2小时
    max-keep-alive-requests: 1000

spring:
  application:
    name: voice-synthesis-backend
  
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************************************************
    username: root
    password: 123456
    
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
      
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: none
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:mapper/*.xml
  type-aliases-package: com.voice.entity
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 自定义配置
voice:
  jwt:
    secret: voice-synthesis-system-jwt-secret-key-2023
    expiration: 86400000 # 24小时
  gpt-sovits:
    api-url: http://api-gpt-sovits.pg-code-go.com
    timeout: 7200000  # 2小时超时
  file:
    upload-path: /uploads/
    audio-path: /uploads/audio/
    max-size: 52428800 # 50MB
  synthesis:
    cost-points: 10
    max-text-length: 500

logging:
  level:
    com.voice: debug
    org.springframework.security: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
