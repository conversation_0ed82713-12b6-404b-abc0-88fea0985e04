<template>
  <div class="user-management">
    <el-card>
      <div slot="header" class="card-header">
        <span>用户管理</span>
        <div class="header-actions">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索用户名、昵称或邮箱"
            style="width: 300px; margin-right: 10px;"
            @keyup.enter.native="handleSearch">
            <i slot="prefix" class="el-icon-search"></i>
          </el-input>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </div>
      </div>

      <!-- 用户列表 -->
      <el-table :data="users" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="username" label="用户名" width="120"></el-table-column>
        <el-table-column prop="nickname" label="昵称" width="120"></el-table-column>
        <el-table-column prop="email" label="邮箱" width="200"></el-table-column>
        <el-table-column prop="phone" label="手机号" width="130"></el-table-column>
        <el-table-column prop="points" label="积分" width="80" sortable></el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="mini">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="注册时间" width="180"></el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button type="text" @click="editUser(scope.row)">编辑</el-button>
            <el-button type="text" @click="managePoints(scope.row)">积分管理</el-button>
            <el-button 
              type="text" 
              :class="scope.row.status === 1 ? 'danger-text' : 'success-text'"
              @click="toggleUserStatus(scope.row)">
              {{ scope.row.status === 1 ? '禁用' : '启用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </el-card>

    <!-- 编辑用户对话框 -->
    <el-dialog title="编辑用户" :visible.sync="editDialogVisible" width="500px">
      <el-form :model="editForm" :rules="editRules" ref="editForm" label-width="80px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="editForm.username" disabled></el-input>
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="editForm.nickname"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editForm.email"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="editForm.phone"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="editForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveUser">保存</el-button>
      </div>
    </el-dialog>

    <!-- 积分管理对话框 -->
    <el-dialog title="积分管理" :visible.sync="pointsDialogVisible" width="400px">
      <el-form :model="pointsForm" :rules="pointsRules" ref="pointsForm" label-width="100px">
        <el-form-item label="当前积分">
          <span style="font-size: 18px; color: #409EFF;">{{ pointsForm.currentPoints }}</span>
        </el-form-item>
        <el-form-item label="操作类型" prop="type">
          <el-radio-group v-model="pointsForm.type">
            <el-radio label="add">增加积分</el-radio>
            <el-radio label="deduct">扣除积分</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="积分数量" prop="points">
          <el-input-number v-model="pointsForm.points" :min="1" :max="10000"></el-input-number>
        </el-form-item>
        <el-form-item label="操作说明" prop="description">
          <el-input v-model="pointsForm.description" placeholder="请输入操作说明"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="pointsDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="savePoints">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getUserList, updateUser, toggleUserStatus } from '@/api/user'
import { adjustUserPoints } from '@/api/points'

export default {
  name: 'UserManagement',
  data() {
    return {
      users: [],
      loading: false,
      searchKeyword: '',
      currentPage: 1,
      pageSize: 20,
      total: 0,
      editDialogVisible: false,
      pointsDialogVisible: false,
      editForm: {
        id: null,
        username: '',
        nickname: '',
        email: '',
        phone: '',
        status: 1
      },
      editRules: {
        nickname: [
          { required: true, message: '请输入昵称', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      },
      pointsForm: {
        userId: null,
        currentPoints: 0,
        type: 'add',
        points: 0,
        description: ''
      },
      pointsRules: {
        type: [
          { required: true, message: '请选择操作类型', trigger: 'change' }
        ],
        points: [
          { required: true, message: '请输入积分数量', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入操作说明', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    async loadUsers() {
      this.loading = true
      try {
        const response = await getUserList({
          page: this.currentPage,
          size: this.pageSize,
          keyword: this.searchKeyword
        })
        this.users = response.data.records || []
        this.total = response.data.total || 0
      } catch (error) {
        console.error('加载用户列表失败:', error)
        this.$message.error('加载用户列表失败')
        // 如果API调用失败，使用模拟数据作为后备
        this.users = [
          {
            id: 1,
            username: 'user001',
            nickname: '张三',
            email: '<EMAIL>',
            phone: '13800138001',
            points: 150,
            status: 1,
            createTime: '2023-12-01 10:30:00'
          },
          {
            id: 2,
            username: 'user002',
            nickname: '李四',
            email: '<EMAIL>',
            phone: '13800138002',
            points: 80,
            status: 1,
            createTime: '2023-12-01 09:15:00'
          }
        ]
        this.total = 2
      } finally {
        this.loading = false
      }
    },

    handleSearch() {
      this.currentPage = 1
      this.loadUsers()
    },

    resetSearch() {
      this.searchKeyword = ''
      this.currentPage = 1
      this.loadUsers()
    },

    handleSizeChange(val) {
      this.pageSize = val
      this.loadUsers()
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.loadUsers()
    },

    editUser(user) {
      this.editForm = { ...user }
      this.editDialogVisible = true
    },

    async saveUser() {
      this.$refs.editForm.validate(async (valid) => {
        if (valid) {
          try {
            await updateUser(this.editForm.id, this.editForm)
            this.$message.success('用户信息更新成功')
            this.editDialogVisible = false
            this.loadUsers()
          } catch (error) {
            console.error('更新用户信息失败:', error)
            this.$message.error('更新用户信息失败')
          }
        }
      })
    },

    managePoints(user) {
      this.pointsForm.userId = user.id
      this.pointsForm.currentPoints = user.points
      this.pointsForm.type = 'add'
      this.pointsForm.points = 0
      this.pointsForm.description = ''
      this.pointsDialogVisible = true
    },

    async savePoints() {
      this.$refs.pointsForm.validate(async (valid) => {
        if (valid) {
          try {
            await adjustUserPoints({
              userId: this.pointsForm.userId,
              type: this.pointsForm.type,
              points: this.pointsForm.points,
              description: this.pointsForm.description
            })
            this.$message.success('积分操作成功')
            this.pointsDialogVisible = false
            this.loadUsers()
          } catch (error) {
            console.error('积分操作失败:', error)
            this.$message.error('积分操作失败')
          }
        }
      })
    },

    async toggleUserStatus(user) {
      const action = user.status === 1 ? '禁用' : '启用'
      this.$confirm(`确定要${action}用户 ${user.username} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await toggleUserStatus(user.id, user.status === 1 ? 0 : 1)
          this.$message.success(`${action}成功`)
          this.loadUsers()
        } catch (error) {
          console.error(`${action}用户失败:`, error)
          this.$message.error(`${action}用户失败`)
        }
      })
    }
  },
  mounted() {
    this.loadUsers()
  }
}
</script>

<style scoped>
.user-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.danger-text {
  color: #F56C6C;
}

.success-text {
  color: #67C23A;
}
</style>
