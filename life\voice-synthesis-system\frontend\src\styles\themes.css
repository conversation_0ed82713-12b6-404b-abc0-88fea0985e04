/* 全局主题样式 */

/* 暗色主题（默认） */
.theme-dark {
  --bg-primary: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  --bg-secondary: rgba(255, 255, 255, 0.05);
  --bg-tertiary: rgba(255, 255, 255, 0.1);
  --bg-card: rgba(255, 255, 255, 0.05);
  --bg-card-hover: rgba(255, 255, 255, 0.1);
  --bg-input: rgba(255, 255, 255, 0.1);
  --bg-button: linear-gradient(135deg, #409EFF, #67C23A);
  --bg-button-secondary: rgba(255, 255, 255, 0.1);
  --bg-header: rgba(15, 15, 35, 0.95);

  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.8);
  --text-tertiary: rgba(255, 255, 255, 0.6);
  --text-disabled: rgba(255, 255, 255, 0.3);
  --text-accent: #409EFF;

  --border-primary: rgba(255, 255, 255, 0.1);
  --border-secondary: rgba(255, 255, 255, 0.2);
  --border-accent: rgba(64, 158, 255, 0.3);

  --shadow-primary: rgba(0, 0, 0, 0.3);
  --shadow-accent: rgba(64, 158, 255, 0.3);
  --shadow-card: rgba(64, 158, 255, 0.2);

  --particle-color: rgba(64, 158, 255, 0.3);
  --grid-color: rgba(64, 158, 255, 0.1);
}

/* 亮色主题 */
.theme-light {
  --bg-primary: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
  --bg-secondary: rgba(0, 0, 0, 0.05);
  --bg-tertiary: rgba(0, 0, 0, 0.1);
  --bg-card: rgba(255, 255, 255, 0.8);
  --bg-card-hover: rgba(255, 255, 255, 0.9);
  --bg-input: rgba(255, 255, 255, 0.9);
  --bg-button: linear-gradient(135deg, #409EFF, #67C23A);
  --bg-button-secondary: rgba(0, 0, 0, 0.1);
  --bg-header: rgba(255, 255, 255, 0.95);

  --text-primary: #2c3e50;
  --text-secondary: rgba(44, 62, 80, 0.8);
  --text-tertiary: rgba(44, 62, 80, 0.6);
  --text-disabled: rgba(44, 62, 80, 0.3);
  --text-accent: #409EFF;

  --border-primary: rgba(0, 0, 0, 0.1);
  --border-secondary: rgba(0, 0, 0, 0.2);
  --border-accent: rgba(64, 158, 255, 0.5);

  --shadow-primary: rgba(0, 0, 0, 0.1);
  --shadow-accent: rgba(64, 158, 255, 0.2);
  --shadow-card: rgba(0, 0, 0, 0.1);

  --particle-color: rgba(64, 158, 255, 0.2);
  --grid-color: rgba(64, 158, 255, 0.05);
}

/* 全局样式应用主题变量 */
body {
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.3s ease;
}

/* 卡片样式 */
.card,
.info-card,
.points-card,
.records-card,
.synthesis-card,
.welcome-card,
.stat-card,
.record-card {
  background: var(--bg-card) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

.card:hover,
.info-card:hover,
.points-card:hover,
.records-card:hover,
.synthesis-card:hover {
  background: var(--bg-card-hover) !important;
  box-shadow: 0 25px 50px var(--shadow-card) !important;
}

/* 输入框样式 */
.tech-input .el-input__inner,
.tech-textarea .el-textarea__inner,
.tech-select .el-input__inner {
  background: var(--bg-input) !important;
  border-color: var(--border-secondary) !important;
  color: var(--text-primary) !important;
}

.tech-input .el-input__inner:focus,
.tech-textarea .el-textarea__inner:focus,
.tech-select .el-input__inner:focus {
  border-color: var(--text-accent) !important;
  box-shadow: 0 0 15px var(--shadow-accent) !important;
}

.tech-input .el-input__inner::placeholder,
.tech-textarea .el-textarea__inner::placeholder {
  color: var(--text-tertiary) !important;
}

/* 按钮样式 */
.primary-btn,
.synthesis-btn,
.save-btn,
.confirm-btn {
  background: var(--bg-button) !important;
}

.secondary-btn,
.reset-btn,
.cancel-btn {
  background: var(--bg-button-secondary) !important;
  border-color: var(--border-secondary) !important;
  color: var(--text-primary) !important;
}

/* 文本颜色 */
.header-title,
.card-title,
.welcome-title,
.username,
.system-title {
  color: var(--text-primary) !important;
}

.points-label,
.stat-label,
.time,
.record-time,
.user-id {
  color: var(--text-tertiary) !important;
}

.points-value,
.stat-number,
.points-number {
  color: var(--text-accent) !important;
}

/* 背景粒子效果 */
.bg-particles {
  background-image:
    radial-gradient(2px 2px at 20px 30px, var(--particle-color), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(103, 194, 58, 0.3), transparent),
    radial-gradient(1px 1px at 90px 40px, var(--particle-color), transparent) !important;
}

.bg-grid {
  background-image:
    linear-gradient(var(--grid-color) 1px, transparent 1px),
    linear-gradient(90deg, var(--grid-color) 1px, transparent 1px) !important;
}

/* 头部导航栏 */
.tech-header {
  background: var(--bg-header) !important;
  border-bottom-color: var(--border-accent) !important;
}

/* 状态指示器颜色调整 */
.theme-light .status-indicator.success,
.theme-light .type-indicator.gain {
  background: rgba(103, 194, 58, 0.3) !important;
}

.theme-light .status-indicator.error,
.theme-light .type-indicator.cost {
  background: rgba(245, 108, 108, 0.3) !important;
}

.theme-light .status-indicator.processing {
  background: rgba(230, 162, 60, 0.3) !important;
}

/* 模态框样式 */
.audio-modal,
.password-modal {
  background: rgba(0, 0, 0, 0.8) !important;
}

.theme-light .audio-modal,
.theme-light .password-modal {
  background: rgba(255, 255, 255, 0.8) !important;
}

.audio-modal-content,
.password-modal-content {
  background: var(--bg-card) !important;
  border-color: var(--border-primary) !important;
}

/* 移动端菜单 */
.mobile-menu {
  background: var(--bg-card) !important;
  border-color: var(--border-primary) !important;
}

.mobile-nav-item {
  color: var(--text-secondary) !important;
}

.mobile-nav-item:hover {
  color: var(--text-accent) !important;
  background: var(--bg-tertiary) !important;
}

.mobile-nav-item.active {
  color: var(--text-accent) !important;
  background: var(--bg-tertiary) !important;
}

/* 移动端主题切换按钮优化 */
.theme-light .mobile-theme-toggle {
  color: var(--text-primary) !important;
  border-top-color: var(--border-primary) !important;
}

.theme-light .mobile-theme-toggle:hover {
  color: var(--text-accent) !important;
  background: var(--bg-tertiary) !important;
}

.theme-dark .mobile-theme-toggle {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--text-accent);
  border-radius: 3px;
  opacity: 0.5;
}

::-webkit-scrollbar-thumb:hover {
  opacity: 0.8;
}

/* 过渡动画 */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
}

/* 主题切换按钮优化 */
.theme-light .theme-toggle-track {
  background: rgba(0, 0, 0, 0.1) !important;
  border-color: rgba(0, 0, 0, 0.2) !important;
}

.theme-light .theme-toggle-track.active {
  background: linear-gradient(135deg, #4A90E2, #357ABD) !important;
  border-color: rgba(64, 158, 255, 0.5) !important;
}

.theme-light .theme-toggle:hover {
  background: rgba(0, 0, 0, 0.05) !important;
}

.theme-light .theme-label {
  color: var(--text-primary) !important;
  font-weight: 600 !important;
}

/* 暗色主题下的主题切换按钮 */
.theme-dark .theme-label {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 600 !important;
}

.theme-dark .theme-toggle:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

/* Element UI 组件主题适配 */
.theme-light .el-dropdown-menu {
  background: var(--bg-card) !important;
  border-color: var(--border-primary) !important;
}

.theme-light .el-dropdown-menu__item {
  color: var(--text-primary) !important;
}

.theme-light .el-dropdown-menu__item:hover {
  background: var(--bg-tertiary) !important;
}

.theme-light .el-select-dropdown {
  background: var(--bg-card) !important;
  border-color: var(--border-primary) !important;
}

.theme-light .el-select-dropdown__item {
  color: var(--text-primary) !important;
}

.theme-light .el-select-dropdown__item:hover {
  background: var(--bg-tertiary) !important;
}