package com.voice.controller;

import com.voice.common.Result;
import com.voice.service.SynthesisRecordService;
import com.voice.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员仪表板控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/dashboard")
@CrossOrigin
public class AdminDashboardController {

    @Autowired
    private UserService userService;

    @Autowired
    private SynthesisRecordService synthesisRecordService;

    /**
     * 获取仪表板统计数据
     */
    @GetMapping("/stats")
    public Result<Map<String, Object>> getDashboardStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 获取用户统计
            long totalUsers = userService.count();
            stats.put("totalUsers", totalUsers);
            
            // 获取合成统计
            long totalSynthesis = synthesisRecordService.count();
            stats.put("totalSynthesis", totalSynthesis);
            
            // 模拟其他统计数据
            stats.put("totalPoints", 86400);
            stats.put("todaySynthesis", 156);
            
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取仪表板统计数据失败", e);
            return Result.error("获取仪表板统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户增长趋势
     */
    @GetMapping("/user-growth")
    public Result<Map<String, Object>> getUserGrowthTrend(
            @RequestParam(defaultValue = "7") Integer days) {
        try {
            Map<String, Object> data = new HashMap<>();
            
            List<String> dates = new ArrayList<>();
            List<Integer> counts = new ArrayList<>();
            
            // 生成最近几天的模拟数据
            for (int i = days - 1; i >= 0; i--) {
                LocalDateTime date = LocalDateTime.now().minusDays(i);
                dates.add(date.format(DateTimeFormatter.ofPattern("MM-dd")));
                counts.add((int) (Math.random() * 50) + 10);
            }
            
            data.put("dates", dates);
            data.put("counts", counts);
            
            return Result.success(data);
        } catch (Exception e) {
            log.error("获取用户增长趋势失败", e);
            return Result.error("获取用户增长趋势失败: " + e.getMessage());
        }
    }

    /**
     * 获取合成使用趋势
     */
    @GetMapping("/synthesis-usage")
    public Result<Map<String, Object>> getSynthesisUsageTrend(
            @RequestParam(defaultValue = "7") Integer days) {
        try {
            Map<String, Object> data = new HashMap<>();
            
            List<String> dates = new ArrayList<>();
            List<Integer> counts = new ArrayList<>();
            
            // 生成最近几天的模拟数据
            for (int i = days - 1; i >= 0; i--) {
                LocalDateTime date = LocalDateTime.now().minusDays(i);
                dates.add(date.format(DateTimeFormatter.ofPattern("MM-dd")));
                counts.add((int) (Math.random() * 100) + 20);
            }
            
            data.put("dates", dates);
            data.put("counts", counts);
            
            return Result.success(data);
        } catch (Exception e) {
            log.error("获取合成使用趋势失败", e);
            return Result.error("获取合成使用趋势失败: " + e.getMessage());
        }
    }

    /**
     * 获取积分消耗趋势
     */
    @GetMapping("/points-consumption")
    public Result<Map<String, Object>> getPointsConsumptionTrend(
            @RequestParam(defaultValue = "7") Integer days) {
        try {
            Map<String, Object> data = new HashMap<>();
            
            List<String> dates = new ArrayList<>();
            List<Integer> counts = new ArrayList<>();
            
            // 生成最近几天的模拟数据
            for (int i = days - 1; i >= 0; i--) {
                LocalDateTime date = LocalDateTime.now().minusDays(i);
                dates.add(date.format(DateTimeFormatter.ofPattern("MM-dd")));
                counts.add((int) (Math.random() * 1000) + 200);
            }
            
            data.put("dates", dates);
            data.put("counts", counts);
            
            return Result.success(data);
        } catch (Exception e) {
            log.error("获取积分消耗趋势失败", e);
            return Result.error("获取积分消耗趋势失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门语音模型
     */
    @GetMapping("/popular-models")
    public Result<List<Map<String, Object>>> getPopularVoiceModels() {
        try {
            List<Map<String, Object>> models = new ArrayList<>();
            
            Map<String, Object> model1 = new HashMap<>();
            model1.put("name", "zhexue_lao");
            model1.put("count", 450);
            models.add(model1);
            
            Map<String, Object> model2 = new HashMap<>();
            model2.put("name", "xiaoshuo_nan");
            model2.put("count", 320);
            models.add(model2);
            
            Map<String, Object> model3 = new HashMap<>();
            model3.put("name", "default");
            model3.put("count", 180);
            models.add(model3);
            
            return Result.success(models);
        } catch (Exception e) {
            log.error("获取热门语音模型失败", e);
            return Result.error("获取热门语音模型失败: " + e.getMessage());
        }
    }

    /**
     * 获取最近活动
     */
    @GetMapping("/recent-activities")
    public Result<Map<String, Object>> getRecentActivities(
            @RequestParam(defaultValue = "10") Integer limit) {
        try {
            Map<String, Object> data = new HashMap<>();
            
            // 获取最近注册用户
            List<Map<String, Object>> recentUsers = new ArrayList<>();
            Map<String, Object> user1 = new HashMap<>();
            user1.put("username", "user001");
            user1.put("nickname", "张三");
            user1.put("points", 100);
            user1.put("createTime", "2023-12-01 10:30:00");
            recentUsers.add(user1);
            
            Map<String, Object> user2 = new HashMap<>();
            user2.put("username", "user002");
            user2.put("nickname", "李四");
            user2.put("points", 100);
            user2.put("createTime", "2023-12-01 09:15:00");
            recentUsers.add(user2);
            
            Map<String, Object> user3 = new HashMap<>();
            user3.put("username", "user003");
            user3.put("nickname", "王五");
            user3.put("points", 100);
            user3.put("createTime", "2023-11-30 16:45:00");
            recentUsers.add(user3);
            
            // 获取最近合成记录
            List<Map<String, Object>> recentSynthesis = new ArrayList<>();
            Map<String, Object> synthesis1 = new HashMap<>();
            synthesis1.put("username", "user001");
            synthesis1.put("text", "这是一个测试文本");
            synthesis1.put("modelName", "zhexue_lao");
            synthesis1.put("status", 1);
            recentSynthesis.add(synthesis1);
            
            Map<String, Object> synthesis2 = new HashMap<>();
            synthesis2.put("username", "user002");
            synthesis2.put("text", "语音合成测试");
            synthesis2.put("modelName", "xiaoshuo_nan");
            synthesis2.put("status", 1);
            recentSynthesis.add(synthesis2);
            
            Map<String, Object> synthesis3 = new HashMap<>();
            synthesis3.put("username", "user003");
            synthesis3.put("text", "Hello World");
            synthesis3.put("modelName", "zhexue_lao");
            synthesis3.put("status", 2);
            recentSynthesis.add(synthesis3);
            
            data.put("recentUsers", recentUsers);
            data.put("recentSynthesis", recentSynthesis);
            
            return Result.success(data);
        } catch (Exception e) {
            log.error("获取最近活动失败", e);
            return Result.error("获取最近活动失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统概览
     */
    @GetMapping("/system-overview")
    public Result<Map<String, Object>> getSystemOverview() {
        try {
            Map<String, Object> overview = new HashMap<>();
            
            overview.put("serverStatus", "running");
            overview.put("databaseStatus", "connected");
            overview.put("gptSovitsStatus", "connected");
            overview.put("lastBackupTime", "2023-12-01 02:00:00");
            
            return Result.success(overview);
        } catch (Exception e) {
            log.error("获取系统概览失败", e);
            return Result.error("获取系统概览失败: " + e.getMessage());
        }
    }
}
