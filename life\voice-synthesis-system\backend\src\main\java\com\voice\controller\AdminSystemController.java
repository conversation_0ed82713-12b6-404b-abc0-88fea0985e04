package com.voice.controller;

import com.voice.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.RuntimeMXBean;
import java.util.HashMap;
import java.util.Map;

/**
 * 管理员系统设置控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/system")
@CrossOrigin
public class AdminSystemController {

    /**
     * 获取系统配置
     */
    @GetMapping("/config")
    public Result<Map<String, Object>> getSystemConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("synthesisCost", 10);
            config.put("registerPoints", 100);
            config.put("gptSovitsApiUrl", "http://127.0.0.1:9880");
            config.put("maxTextLength", 500);
            config.put("audioStoragePath", "/uploads/audio/");
            
            return Result.success(config);
        } catch (Exception e) {
            log.error("获取系统配置失败", e);
            return Result.error("获取系统配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新系统配置
     */
    @PutMapping("/config")
    public Result<String> updateSystemConfig(@RequestBody Map<String, Object> config) {
        try {
            // 这里可以实现更新系统配置的逻辑
            // 可以将配置保存到数据库或配置文件中
            log.info("更新系统配置: {}", config);
            
            return Result.success("系统配置更新成功");
        } catch (Exception e) {
            log.error("更新系统配置失败", e);
            return Result.error("更新系统配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统状态
     */
    @GetMapping("/status")
    public Result<Map<String, Object>> getSystemStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            
            // 获取运行时信息
            RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();
            long uptime = runtimeBean.getUptime();
            
            // 转换运行时间为可读格式
            long days = uptime / (24 * 60 * 60 * 1000);
            long hours = (uptime % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000);
            long minutes = (uptime % (60 * 60 * 1000)) / (60 * 1000);
            
            status.put("version", "1.0.0");
            status.put("uptime", String.format("%d天%d小时%d分钟", days, hours, minutes));
            status.put("javaVersion", System.getProperty("java.version"));
            status.put("osName", System.getProperty("os.name"));
            
            // 获取内存信息
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            long usedMemory = memoryBean.getHeapMemoryUsage().getUsed() / (1024 * 1024);
            long maxMemory = memoryBean.getHeapMemoryUsage().getMax() / (1024 * 1024);
            status.put("memoryUsage", usedMemory + "MB / " + maxMemory + "MB");
            
            // 模拟磁盘使用情况
            status.put("diskUsage", "15GB / 100GB");
            
            // 模拟数据库连接状态
            status.put("dbStatus", "connected");
            
            // 模拟GPT-SoVITS连接状态
            status.put("gptSovitsStatus", "connected");
            
            return Result.success(status);
        } catch (Exception e) {
            log.error("获取系统状态失败", e);
            return Result.error("获取系统状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统日志
     */
    @GetMapping("/logs")
    public Result<Map<String, Object>> getSystemLogs(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "50") Integer size) {
        try {
            // 这里可以实现获取系统日志的逻辑
            Map<String, Object> data = new HashMap<>();
            data.put("logs", new java.util.ArrayList<>());
            data.put("total", 0);
            
            return Result.success(data);
        } catch (Exception e) {
            log.error("获取系统日志失败", e);
            return Result.error("获取系统日志失败: " + e.getMessage());
        }
    }

    /**
     * 清理系统缓存
     */
    @PostMapping("/clear-cache")
    public Result<String> clearSystemCache() {
        try {
            // 这里可以实现清理系统缓存的逻辑
            System.gc(); // 建议垃圾回收
            
            return Result.success("系统缓存清理成功");
        } catch (Exception e) {
            log.error("清理系统缓存失败", e);
            return Result.error("清理系统缓存失败: " + e.getMessage());
        }
    }

    /**
     * 备份数据库
     */
    @PostMapping("/backup")
    public Result<String> backupDatabase() {
        try {
            // 这里可以实现数据库备份的逻辑
            return Result.success("数据库备份成功");
        } catch (Exception e) {
            log.error("数据库备份失败", e);
            return Result.error("数据库备份失败: " + e.getMessage());
        }
    }

    /**
     * 获取备份列表
     */
    @GetMapping("/backups")
    public Result<java.util.List<Map<String, Object>>> getBackupList() {
        try {
            // 这里可以实现获取备份列表的逻辑
            java.util.List<Map<String, Object>> backups = new java.util.ArrayList<>();
            
            return Result.success(backups);
        } catch (Exception e) {
            log.error("获取备份列表失败", e);
            return Result.error("获取备份列表失败: " + e.getMessage());
        }
    }

    /**
     * 恢复数据库
     */
    @PostMapping("/restore/{backupId}")
    public Result<String> restoreDatabase(@PathVariable String backupId) {
        try {
            // 这里可以实现数据库恢复的逻辑
            return Result.success("数据库恢复成功");
        } catch (Exception e) {
            log.error("数据库恢复失败", e);
            return Result.error("数据库恢复失败: " + e.getMessage());
        }
    }
}
