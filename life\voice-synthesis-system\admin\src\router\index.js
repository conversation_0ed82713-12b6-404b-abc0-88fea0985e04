import Vue from 'vue'
import Router from 'vue-router'
import Cookies from 'js-cookie'

Vue.use(Router)

const router = new Router({
  routes: [
    {
      path: '/',
      redirect: '/layout/dashboard'
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login'),
      meta: { requiresAuth: false }
    },
    {
      path: '/layout',
      component: () => import('@/layout/Layout'),
      meta: { requiresAuth: true },
      redirect: '/layout/dashboard',
      children: [
        {
          path: 'dashboard',
          name: 'Dashboard',
          component: () => import('@/views/Dashboard'),
          meta: { title: '仪表板' }
        },
        {
          path: 'users',
          name: 'UserManagement',
          component: () => import('@/views/UserManagement'),
          meta: { title: '用户管理' }
        },
        {
          path: 'synthesis',
          name: 'SynthesisManagement',
          component: () => import('@/views/SynthesisManagement'),
          meta: { title: '合成管理' }
        },
        {
          path: 'points',
          name: 'PointsManagement',
          component: () => import('@/views/PointsManagement'),
          meta: { title: '积分管理' }
        },
        {
          path: 'settings',
          name: 'SystemSettings',
          component: () => import('@/views/SystemSettings'),
          meta: { title: '系统设置' }
        },
        {
          path: 'api-test',
          name: 'ApiTest',
          component: () => import('@/views/ApiTest'),
          meta: { title: 'API测试' }
        }
      ]
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = Cookies.get('admin_token')

  if (to.meta.requiresAuth && !token) {
    next('/login')
  } else if (to.path === '/login' && token) {
    next('/layout/dashboard')
  } else {
    next()
  }
})

export default router
