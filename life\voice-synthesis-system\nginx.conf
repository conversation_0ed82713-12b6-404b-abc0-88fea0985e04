# Nginx配置文件
# 用于解决语音合成长时间请求的504超时问题

server {
    listen 80;
    server_name localhost;

    # 设置客户端请求体大小限制
    client_max_body_size 100M;
    
    # 设置各种超时时间为2小时
    proxy_connect_timeout 7200s;
    proxy_send_timeout 7200s;
    proxy_read_timeout 7200s;
    send_timeout 7200s;
    client_header_timeout 7200s;
    client_body_timeout 7200s;
    
    # 设置缓冲区大小
    proxy_buffer_size 64k;
    proxy_buffers 32 32k;
    proxy_busy_buffers_size 128k;
    
    # 前端静态文件
    location / {
        root /path/to/your/frontend/dist;
        try_files $uri $uri/ /index.html;
        index index.html;
    }
    
    # 后端API代理
    location /api/ {
        proxy_pass http://localhost:8080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 禁用缓存
        proxy_cache off;
        proxy_no_cache 1;
        proxy_cache_bypass 1;
        
        # 支持WebSocket（如果需要）
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 音频文件访问
    location /audio/ {
        proxy_pass http://localhost:8080/api/audio/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 错误页面
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}

# 如果使用HTTPS，添加以下配置
# server {
#     listen 443 ssl;
#     server_name your-domain.com;
#     
#     ssl_certificate /path/to/your/certificate.crt;
#     ssl_certificate_key /path/to/your/private.key;
#     
#     # 其他配置与上面相同...
# }
