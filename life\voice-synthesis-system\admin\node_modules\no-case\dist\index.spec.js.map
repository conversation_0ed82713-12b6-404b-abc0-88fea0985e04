{"version": 3, "file": "index.spec.js", "sourceRoot": "", "sources": ["../src/index.spec.ts"], "names": [], "mappings": ";;AAAA,sBAAoC;AAEpC,IAAM,UAAU,GAAiC;IAC/C,gBAAgB;IAChB,CAAC,MAAM,EAAE,MAAM,CAAC;IAChB,CAAC,MAAM,EAAE,MAAM,CAAC;IAEhB,cAAc;IACd,CAAC,YAAY,EAAE,aAAa,CAAC;IAC7B,CAAC,eAAe,EAAE,gBAAgB,CAAC;IACnC,CAAC,kBAAkB,EAAE,mBAAmB,CAAC;IACzC,CAAC,OAAO,EAAE,OAAO,CAAC;IAClB,CAAC,WAAW,EAAE,aAAa,CAAC;IAC5B,CAAC,aAAa,EAAE,cAAc,CAAC;IAC/B,CAAC,aAAa,EAAE,cAAc,CAAC;IAC/B,CAAC,YAAY,EAAE,YAAY,CAAC;IAC5B,CAAC,SAAS,EAAE,UAAU,CAAC;IAEvB,iBAAiB;IACjB,CAAC,gBAAgB,EAAE,eAAe,CAAC;IACnC,CAAC,cAAc,EAAE,cAAc,CAAC;IAEhC,gBAAgB;IAChB,CAAC,SAAS,EAAE,SAAS,CAAC;IACtB,CAAC,gBAAgB,EAAE,kBAAkB,CAAC;IACtC,CAAC,aAAa,EAAE,gBAAgB,CAAC;IAEjC,+BAA+B;IAC/B,CAAC,UAAU,EAAE,UAAU,CAAC;IACxB,CAAC,WAAW,EAAE,WAAW,CAAC;IAC1B,CAAC,YAAY,EAAE,YAAY,CAAC;IAC5B,CAAC,eAAe,EAAE,eAAe,CAAC;IAClC,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IAEpC,eAAe;IACf,CAAC,UAAU,EAAE,QAAQ,CAAC;IAEtB,8BAA8B;IAC9B,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IACpC,CAAC,kBAAkB,EAAE,gBAAgB,CAAC;IACtC,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IAEpC,cAAc;IACd,CAAC,UAAU,EAAE,MAAM,CAAC;IAEpB,uBAAuB;IACvB,CAAC,sBAAsB,EAAE,sBAAsB,CAAC;IAEhD,uDAAuD;IACvD,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IACpC,CAAC,YAAY,EAAE,YAAY,CAAC;IAE5B,iBAAiB;IACjB,CAAC,WAAW,EAAE,YAAY,EAAE,EAAE,WAAW,EAAE,oBAAoB,EAAE,CAAC;IAClE,CAAC,YAAY,EAAE,aAAa,EAAE,EAAE,WAAW,EAAE,oBAAoB,EAAE,CAAC;CACrE,CAAC;AAEF,QAAQ,CAAC,SAAS,EAAE;4BACN,KAAK,EAAE,MAAM,EAAE,OAAO;QAChC,EAAE,CAAI,KAAK,YAAO,MAAQ,EAAE;YAC1B,MAAM,CAAC,SAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;;IAHL,KAAuC,UAAU,EAAV,yBAAU,EAAV,wBAAU,EAAV,IAAU;QAAtC,IAAA,qBAAwB,EAAvB,KAAK,QAAA,EAAE,MAAM,QAAA,EAAE,OAAO,QAAA;gBAAtB,KAAK,EAAE,MAAM,EAAE,OAAO;KAIjC;AACH,CAAC,CAAC,CAAC", "sourcesContent": ["import { noCase, Options } from \".\";\n\nconst TEST_CASES: [string, string, Options?][] = [\n  // Single words.\n  [\"test\", \"test\"],\n  [\"TEST\", \"test\"],\n\n  // Camel case.\n  [\"testString\", \"test string\"],\n  [\"testString123\", \"test string123\"],\n  [\"testString_1_2_3\", \"test string 1 2 3\"],\n  [\"x_256\", \"x 256\"],\n  [\"anHTMLTag\", \"an html tag\"],\n  [\"ID123String\", \"id123 string\"],\n  [\"Id123String\", \"id123 string\"],\n  [\"foo bar123\", \"foo bar123\"],\n  [\"a1bStar\", \"a1b star\"],\n\n  // Constant case.\n  [\"CONSTANT_CASE \", \"constant case\"],\n  [\"CONST123_FOO\", \"const123 foo\"],\n\n  // Random cases.\n  [\"FOO_bar\", \"foo bar\"],\n  [\"XMLHttpRequest\", \"xml http request\"],\n  [\"IQueryAArgs\", \"i query a args\"],\n\n  // Non-alphanumeric separators.\n  [\"dot.case\", \"dot case\"],\n  [\"path/case\", \"path case\"],\n  [\"snake_case\", \"snake case\"],\n  [\"snake_case123\", \"snake case123\"],\n  [\"snake_case_123\", \"snake case 123\"],\n\n  // Punctuation.\n  ['\"quotes\"', \"quotes\"],\n\n  // Space between number parts.\n  [\"version 0.45.0\", \"version 0 45 0\"],\n  [\"version 0..78..9\", \"version 0 78 9\"],\n  [\"version 4_99/4\", \"version 4 99 4\"],\n\n  // Whitespace.\n  [\"  test  \", \"test\"],\n\n  // Number string input.\n  [\"something_2014_other\", \"something 2014 other\"],\n\n  // https://github.com/blakeembrey/change-case/issues/21\n  [\"amazon s3 data\", \"amazon s3 data\"],\n  [\"foo_13_bar\", \"foo 13 bar\"],\n\n  // Customization.\n  [\"camel2019\", \"camel 2019\", { splitRegexp: /([a-z])([A-Z0-9])/g }],\n  [\"minifyURLs\", \"minify urls\", { splitRegexp: /([a-z])([A-Z0-9])/g }],\n];\n\ndescribe(\"no case\", () => {\n  for (const [input, result, options] of TEST_CASES) {\n    it(`${input} -> ${result}`, () => {\n      expect(noCase(input, options)).toEqual(result);\n    });\n  }\n});\n"]}