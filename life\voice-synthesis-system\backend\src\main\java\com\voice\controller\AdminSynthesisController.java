package com.voice.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.voice.common.Result;
import com.voice.entity.SynthesisRecord;
import com.voice.service.SynthesisRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员合成管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/admin")
@CrossOrigin
public class AdminSynthesisController {

    @Autowired
    private SynthesisRecordService synthesisRecordService;

    /**
     * 获取合成记录列表
     */
    @GetMapping("/synthesis-records")
    public Result<Map<String, Object>> getSynthesisRecords(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) Integer status) {
        try {
            Page<SynthesisRecord> pageParam = new Page<>(page, size);
            QueryWrapper<SynthesisRecord> queryWrapper = new QueryWrapper<>();
            
            if (status != null) {
                queryWrapper.eq("status", status);
            }
            
            queryWrapper.orderByDesc("create_time");
            
            IPage<SynthesisRecord> recordPage = synthesisRecordService.page(pageParam, queryWrapper);
            
            Map<String, Object> data = new HashMap<>();
            data.put("records", recordPage.getRecords());
            data.put("total", recordPage.getTotal());
            data.put("pages", recordPage.getPages());
            data.put("current", recordPage.getCurrent());
            data.put("size", recordPage.getSize());
            
            return Result.success(data);
        } catch (Exception e) {
            log.error("获取合成记录失败", e);
            return Result.error("获取合成记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取合成统计信息
     */
    @GetMapping("/synthesis/stats")
    public Result<Map<String, Object>> getSynthesisStats() {
        try {
            Map<String, Object> stats = synthesisRecordService.getSynthesisStats();
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取合成统计信息失败", e);
            return Result.error("获取合成统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取合成记录详情
     */
    @GetMapping("/synthesis-records/{id}")
    public Result<SynthesisRecord> getSynthesisDetail(@PathVariable Long id) {
        try {
            SynthesisRecord record = synthesisRecordService.getById(id);
            if (record == null) {
                return Result.error("合成记录不存在");
            }
            
            return Result.success(record);
        } catch (Exception e) {
            log.error("获取合成记录详情失败", e);
            return Result.error("获取合成记录详情失败: " + e.getMessage());
        }
    }

    /**
     * 删除合成记录
     */
    @DeleteMapping("/synthesis-records/{id}")
    public Result<String> deleteSynthesisRecord(@PathVariable Long id) {
        try {
            boolean success = synthesisRecordService.removeById(id);
            
            if (success) {
                return Result.success("删除合成记录成功");
            } else {
                return Result.error("删除合成记录失败");
            }
        } catch (Exception e) {
            log.error("删除合成记录失败", e);
            return Result.error("删除合成记录失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除合成记录
     */
    @PostMapping("/synthesis-records/batch-delete")
    public Result<String> batchDeleteSynthesisRecords(@RequestBody Map<String, List<Long>> request) {
        try {
            List<Long> ids = request.get("ids");
            boolean success = synthesisRecordService.removeByIds(ids);
            
            if (success) {
                return Result.success("批量删除合成记录成功");
            } else {
                return Result.error("批量删除合成记录失败");
            }
        } catch (Exception e) {
            log.error("批量删除合成记录失败", e);
            return Result.error("批量删除合成记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取语音模型列表
     */
    @GetMapping("/voice-models")
    public Result<List<Map<String, Object>>> getVoiceModels() {
        try {
            List<Map<String, Object>> models = synthesisRecordService.getVoiceModels();
            return Result.success(models);
        } catch (Exception e) {
            log.error("获取语音模型列表失败", e);
            return Result.error("获取语音模型列表失败: " + e.getMessage());
        }
    }

    /**
     * 创建语音模型
     */
    @PostMapping("/voice-models")
    public Result<String> createVoiceModel(@RequestBody Map<String, Object> model) {
        try {
            // 这里可以实现创建语音模型的逻辑
            // 暂时返回成功
            return Result.success("创建语音模型成功");
        } catch (Exception e) {
            log.error("创建语音模型失败", e);
            return Result.error("创建语音模型失败: " + e.getMessage());
        }
    }

    /**
     * 更新语音模型
     */
    @PutMapping("/voice-models/{id}")
    public Result<String> updateVoiceModel(@PathVariable Long id, @RequestBody Map<String, Object> model) {
        try {
            // 这里可以实现更新语音模型的逻辑
            // 暂时返回成功
            return Result.success("更新语音模型成功");
        } catch (Exception e) {
            log.error("更新语音模型失败", e);
            return Result.error("更新语音模型失败: " + e.getMessage());
        }
    }

    /**
     * 删除语音模型
     */
    @DeleteMapping("/voice-models/{id}")
    public Result<String> deleteVoiceModel(@PathVariable Long id) {
        try {
            // 这里可以实现删除语音模型的逻辑
            // 暂时返回成功
            return Result.success("删除语音模型成功");
        } catch (Exception e) {
            log.error("删除语音模型失败", e);
            return Result.error("删除语音模型失败: " + e.getMessage());
        }
    }

    /**
     * 导出合成记录
     */
    @GetMapping("/synthesis-records/export")
    public Result<String> exportSynthesisRecords(
            @RequestParam(required = false) Integer status) {
        try {
            // 这里可以实现导出合成记录的逻辑
            // 暂时返回成功
            return Result.success("合成记录导出成功");
        } catch (Exception e) {
            log.error("导出合成记录失败", e);
            return Result.error("导出合成记录失败: " + e.getMessage());
        }
    }
}
