const axios = require('axios');

// 测试管理员登录接口
async function testAdminLogin() {
    try {
        console.log('测试管理员登录接口...');
        
        const response = await axios.post('http://localhost:8080/api/admin/login', {
            username: 'admin',
            password: 'admin123'
        }, {
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        console.log('登录成功!');
        console.log('响应状态:', response.status);
        console.log('响应数据:', JSON.stringify(response.data, null, 2));
        
        // 测试获取管理员信息
        if (response.data && response.data.data && response.data.data.token) {
            const token = response.data.data.token;
            console.log('\n测试获取管理员信息...');
            
            const infoResponse = await axios.get('http://localhost:8080/api/admin/info', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            
            console.log('获取管理员信息成功!');
            console.log('管理员信息:', JSON.stringify(infoResponse.data, null, 2));
        }
        
    } catch (error) {
        console.error('测试失败:');
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        } else {
            console.error('错误信息:', error.message);
        }
    }
}

// 运行测试
testAdminLogin();
