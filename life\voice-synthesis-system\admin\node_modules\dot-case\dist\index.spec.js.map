{"version": 3, "file": "index.spec.js", "sourceRoot": "", "sources": ["../src/index.spec.ts"], "names": [], "mappings": ";;AAAA,sBAA4B;AAE5B,IAAM,UAAU,GAAuB;IACrC,CAAC,EAAE,EAAE,EAAE,CAAC;IACR,CAAC,MAAM,EAAE,MAAM,CAAC;IAChB,CAAC,aAAa,EAAE,aAAa,CAAC;IAC9B,CAAC,aAAa,EAAE,aAAa,CAAC;IAC9B,CAAC,UAAU,EAAE,UAAU,CAAC;IACxB,CAAC,WAAW,EAAE,WAAW,CAAC;IAC1B,CAAC,QAAQ,EAAE,SAAS,CAAC;IACrB,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IACpC,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;CACrC,CAAC;AAEF,QAAQ,CAAC,UAAU,EAAE;4BACP,KAAK,EAAE,MAAM;QACvB,EAAE,CAAI,KAAK,YAAO,MAAQ,EAAE;YAC1B,MAAM,CAAC,UAAO,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;;IAHL,KAA8B,UAAU,EAAV,yBAAU,EAAV,wBAAU,EAAV,IAAU;QAA7B,IAAA,qBAAe,EAAd,KAAK,QAAA,EAAE,MAAM,QAAA;gBAAb,KAAK,EAAE,MAAM;KAIxB;AACH,CAAC,CAAC,CAAC", "sourcesContent": ["import { dotCase } from \".\";\n\nconst TEST_CASES: [string, string][] = [\n  [\"\", \"\"],\n  [\"test\", \"test\"],\n  [\"test string\", \"test.string\"],\n  [\"Test String\", \"test.string\"],\n  [\"dot.case\", \"dot.case\"],\n  [\"path/case\", \"path.case\"],\n  [\"TestV2\", \"test.v2\"],\n  [\"version 1.2.10\", \"version.1.2.10\"],\n  [\"version 1.21.0\", \"version.1.21.0\"],\n];\n\ndescribe(\"dot case\", () => {\n  for (const [input, result] of TEST_CASES) {\n    it(`${input} -> ${result}`, () => {\n      expect(dotCase(input)).toEqual(result);\n    });\n  }\n});\n"]}