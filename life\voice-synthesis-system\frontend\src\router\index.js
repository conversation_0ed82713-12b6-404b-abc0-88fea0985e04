import Vue from 'vue'
import Router from 'vue-router'
import Cookies from 'js-cookie'

Vue.use(Router)

const router = new Router({
  routes: [
    {
      path: '/',
      redirect: '/layout/home'
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login'),
      meta: { requiresAuth: false }
    },
    {
      path: '/register',
      name: 'Register',
      component: () => import('@/views/Register'),
      meta: { requiresAuth: false }
    },
    {
      path: '/layout',
      component: () => import('@/layout/Layout'),
      meta: { requiresAuth: true },
      redirect: '/layout/home',
      children: [
        {
          path: 'home',
          name: 'Home',
          component: () => import('@/views/Home'),
          meta: { title: '首页' }
        },
        {
          path: 'synthesis',
          name: 'Synthesis',
          component: () => import('@/views/Synthesis'),
          meta: { title: '语音合成' }
        },
        {
          path: 'records',
          name: 'Records',
          component: () => import('@/views/Records'),
          meta: { title: '合成记录' }
        },
        {
          path: 'profile',
          name: 'Profile',
          component: () => import('@/views/Profile'),
          meta: { title: '个人中心' }
        }
      ]
    },
    // 兼容旧路由，重定向到新路由
    {
      path: '/home',
      redirect: '/layout/home'
    },
    {
      path: '/synthesis',
      redirect: '/layout/synthesis'
    },
    {
      path: '/records',
      redirect: '/layout/records'
    },
    {
      path: '/profile',
      redirect: '/layout/profile'
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = Cookies.get('token')

  if (to.meta.requiresAuth && !token) {
    next('/login')
  } else if ((to.path === '/login' || to.path === '/register') && token) {
    next('/layout/home')
  } else {
    next()
  }
})

export default router
