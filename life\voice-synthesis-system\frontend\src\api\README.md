# API 统一管理

本目录包含了前端项目中所有API请求的统一管理模块，提供了结构化、可维护的API调用方式。

## 目录结构

```
src/api/
├── index.js          # axios实例配置和拦截器
├── config.js         # API配置文件
├── auth.js           # 认证相关API
├── voice.js          # 语音合成相关API
├── user.js           # 用户相关API
├── api.js            # 统一导出所有API
└── README.md         # 说明文档
```

## 使用方式

### 1. 在组件中使用统一API

```javascript
// 在Vue组件中使用
export default {
  methods: {
    async handleLogin() {
      try {
        const response = await this.$api.auth.login({
          username: 'user',
          password: 'password'
        })
        console.log(response.data)
      } catch (error) {
        console.error('登录失败:', error)
      }
    },
    
    async loadVoiceModels() {
      try {
        const response = await this.$api.voice.getModels()
        this.models = response.data.models
      } catch (error) {
        console.error('加载模型失败:', error)
      }
    }
  }
}
```

### 2. 按需导入API模块

```javascript
// 导入特定模块
import { auth, voice, user } from '@/api/api'

// 使用
const response = await auth.login(loginData)
const models = await voice.getModels()
const userInfo = await user.getStats()
```

### 3. 直接使用axios实例

```javascript
import request from '@/api/index'

// 自定义请求
const response = await request({
  url: '/custom/endpoint',
  method: 'post',
  data: { key: 'value' }
})
```

## API模块说明

### auth.js - 认证相关
- `login(data)` - 用户登录
- `register(data)` - 用户注册
- `getUserInfo()` - 获取用户信息
- `refreshToken()` - 刷新token
- `logout()` - 用户登出

### voice.js - 语音合成相关
- `getVoiceModels()` - 获取语音模型列表
- `synthesizeVoice(data)` - 语音合成
- `getSynthesisRecords(params)` - 获取合成记录
- `deleteSynthesisRecord(id)` - 删除合成记录
- `batchDeleteSynthesisRecords(ids)` - 批量删除记录
- `downloadAudio(audioUrl)` - 下载音频文件

### user.js - 用户相关
- `getUserStats()` - 获取用户统计信息
- `updateUserProfile(data)` - 更新用户资料
- `changePassword(data)` - 修改密码
- `getPointRecords(params)` - 获取积分记录
- `getUserPoints()` - 获取用户积分余额
- `rechargePoints(data)` - 充值积分
- `uploadAvatar(formData)` - 上传头像

## 配置说明

### config.js 配置项
- `API_CONFIG` - API基础配置（URL、超时时间等）
- `API_ENDPOINTS` - API端点配置
- `HTTP_STATUS` - HTTP状态码常量
- `ERROR_MESSAGES` - 错误消息常量

## 拦截器功能

### 请求拦截器
- 自动添加Authorization头部
- 统一处理请求配置

### 响应拦截器
- 统一处理响应数据格式
- 自动处理错误状态码
- 401状态码自动跳转登录页面
- 统一错误消息提示

## 优势

1. **统一管理** - 所有API请求集中管理，便于维护
2. **类型安全** - 明确的API接口定义
3. **错误处理** - 统一的错误处理机制
4. **配置灵活** - 支持不同环境的配置
5. **易于扩展** - 模块化设计，便于添加新的API
6. **代码复用** - 避免重复的axios配置代码

## 迁移指南

### 从旧的API调用方式迁移

**旧方式:**
```javascript
const response = await this.$http.get('/api/voice/models')
```

**新方式:**
```javascript
const response = await this.$api.voice.getModels()
```

### 批量替换建议

1. 搜索项目中的 `this.$http.get`、`this.$http.post` 等
2. 根据API端点找到对应的新API方法
3. 替换为新的调用方式
4. 测试功能是否正常

## 注意事项

1. 确保在main.js中正确导入和挂载API
2. 新增API时记得在对应模块中添加方法
3. 修改API端点时更新config.js中的配置
4. 保持API方法命名的一致性和语义化
