import Vue from 'vue'
import Vuex from 'vuex'
import Cookies from 'js-cookie'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    user: Cookies.get('user') ? JSON.parse(Cookies.get('user')) : null,
    token: Cookies.get('token') || null,
    theme: localStorage.getItem('theme') || 'dark' // 默认暗色主题
  },

  mutations: {
    SET_USER(state, user) {
      state.user = user
      if (user) {
        Cookies.set('user', JSON.stringify(user), { expires: 7 })
      } else {
        Cookies.remove('user')
      }
    },

    SET_TOKEN(state, token) {
      state.token = token
      if (token) {
        Cookies.set('token', token, { expires: 7 })
      } else {
        Cookies.remove('token')
      }
    },

    UPDATE_USER_POINTS(state, points) {
      if (state.user) {
        state.user.points = points
        Cookies.set('user', JSON.stringify(state.user), { expires: 7 })
      }
    },

    LOGOUT(state) {
      state.user = null
      state.token = null
      Cookies.remove('user')
      Cookies.remove('token')
    },

    SET_THEME(state, theme) {
      state.theme = theme
      localStorage.setItem('theme', theme)
      // 更新HTML根元素的class
      document.documentElement.className = `theme-${theme}`
    }
  },

  actions: {
    login({ commit }, { user, token }) {
      commit('SET_USER', user)
      commit('SET_TOKEN', token)
    },

    logout({ commit }) {
      commit('LOGOUT')
    },

    updateUserPoints({ commit }, points) {
      commit('UPDATE_USER_POINTS', points)
    },

    toggleTheme({ commit, state }) {
      const newTheme = state.theme === 'dark' ? 'light' : 'dark'
      commit('SET_THEME', newTheme)
    },

    setTheme({ commit }, theme) {
      commit('SET_THEME', theme)
    }
  },

  getters: {
    isLoggedIn: state => !!state.token,
    currentUser: state => state.user,
    userPoints: state => state.user ? state.user.points : 0,
    currentTheme: state => state.theme,
    isDarkTheme: state => state.theme === 'dark',
    isLightTheme: state => state.theme === 'light'
  }
})
