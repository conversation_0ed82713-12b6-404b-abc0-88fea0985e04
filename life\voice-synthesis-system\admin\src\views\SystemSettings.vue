<template>
  <div class="system-settings">
    <el-card>
      <div slot="header">
        <span>系统设置</span>
      </div>

      <el-form :model="settings" :rules="rules" ref="settingsForm" label-width="150px">
        <el-form-item label="语音合成消耗积分" prop="synthesisCost">
          <el-input-number v-model="settings.synthesisCost" :min="1" :max="100"></el-input-number>
          <span style="margin-left: 10px; color: #666;">积分/次</span>
        </el-form-item>

        <el-form-item label="注册赠送积分" prop="registerPoints">
          <el-input-number v-model="settings.registerPoints" :min="0" :max="1000"></el-input-number>
          <span style="margin-left: 10px; color: #666;">积分</span>
        </el-form-item>

        <el-form-item label="GPT-SoVITS API地址" prop="gptSovitsApiUrl">
          <el-input v-model="settings.gptSovitsApiUrl" placeholder="http://127.0.0.1:9880"></el-input>
        </el-form-item>

        <el-form-item label="单次合成文本最大长度" prop="maxTextLength">
          <el-input-number v-model="settings.maxTextLength" :min="100" :max="2000"></el-input-number>
          <span style="margin-left: 10px; color: #666;">字符</span>
        </el-form-item>

        <el-form-item label="音频文件存储路径" prop="audioStoragePath">
          <el-input v-model="settings.audioStoragePath" placeholder="/uploads/audio/"></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="saveSettings" :loading="saving">保存设置</el-button>
          <el-button @click="resetSettings">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 系统信息 -->
    <el-card style="margin-top: 20px;">
      <div slot="header">
        <span>系统信息</span>
        <el-button style="float: right;" type="text" @click="refreshSystemInfo">刷新</el-button>
      </div>

      <el-descriptions :column="2" border>
        <el-descriptions-item label="系统版本">{{ systemInfo.version }}</el-descriptions-item>
        <el-descriptions-item label="运行时间">{{ systemInfo.uptime }}</el-descriptions-item>
        <el-descriptions-item label="Java版本">{{ systemInfo.javaVersion }}</el-descriptions-item>
        <el-descriptions-item label="操作系统">{{ systemInfo.osName }}</el-descriptions-item>
        <el-descriptions-item label="内存使用">{{ systemInfo.memoryUsage }}</el-descriptions-item>
        <el-descriptions-item label="磁盘使用">{{ systemInfo.diskUsage }}</el-descriptions-item>
        <el-descriptions-item label="数据库状态">
          <el-tag :type="systemInfo.dbStatus === 'connected' ? 'success' : 'danger'">
            {{ systemInfo.dbStatus === 'connected' ? '已连接' : '连接失败' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="GPT-SoVITS状态">
          <el-tag :type="systemInfo.gptSovitsStatus === 'connected' ? 'success' : 'danger'">
            {{ systemInfo.gptSovitsStatus === 'connected' ? '已连接' : '连接失败' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 操作日志 -->
    <el-card style="margin-top: 20px;">
      <div slot="header">
        <span>最近操作日志</span>
      </div>

      <el-table :data="operationLogs" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="adminName" label="操作员" width="120"></el-table-column>
        <el-table-column prop="operation" label="操作" width="150"></el-table-column>
        <el-table-column prop="target" label="操作对象" show-overflow-tooltip></el-table-column>
        <el-table-column prop="result" label="结果" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.result === 'success' ? 'success' : 'danger'" size="mini">
              {{ scope.row.result === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="操作时间" width="180"></el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { getSystemConfig, updateSystemConfig, getSystemStatus } from '@/api/system'

export default {
  name: 'SystemSettings',
  data() {
    return {
      settings: {
        synthesisCost: 10,
        registerPoints: 100,
        gptSovitsApiUrl: 'http://127.0.0.1:9880',
        maxTextLength: 500,
        audioStoragePath: '/uploads/audio/'
      },
      rules: {
        synthesisCost: [
          { required: true, message: '请输入语音合成消耗积分', trigger: 'blur' }
        ],
        registerPoints: [
          { required: true, message: '请输入注册赠送积分', trigger: 'blur' }
        ],
        gptSovitsApiUrl: [
          { required: true, message: '请输入GPT-SoVITS API地址', trigger: 'blur' }
        ],
        maxTextLength: [
          { required: true, message: '请输入单次合成文本最大长度', trigger: 'blur' }
        ],
        audioStoragePath: [
          { required: true, message: '请输入音频文件存储路径', trigger: 'blur' }
        ]
      },
      saving: false,
      systemInfo: {
        version: '1.0.0',
        uptime: '2天3小时45分钟',
        javaVersion: 'OpenJDK 1.8.0_292',
        osName: 'Windows 10',
        memoryUsage: '512MB / 2GB',
        diskUsage: '15GB / 100GB',
        dbStatus: 'connected',
        gptSovitsStatus: 'connected'
      },
      operationLogs: []
    }
  },
  methods: {
    async loadSettings() {
      try {
        const response = await getSystemConfig()
        this.settings = response.data || this.settings
      } catch (error) {
        console.error('加载系统设置失败:', error)
        this.$message.error('加载系统设置失败')
      }
    },

    async saveSettings() {
      this.$refs.settingsForm.validate(async (valid) => {
        if (valid) {
          this.saving = true
          try {
            await updateSystemConfig(this.settings)
            this.$message.success('系统设置保存成功')
          } catch (error) {
            console.error('保存系统设置失败:', error)
            this.$message.error('保存系统设置失败')
          } finally {
            this.saving = false
          }
        }
      })
    },

    resetSettings() {
      this.loadSettings()
    },

    async refreshSystemInfo() {
      try {
        const response = await getSystemStatus()
        this.systemInfo = response.data || this.systemInfo
        this.$message.success('系统信息已刷新')
      } catch (error) {
        console.error('刷新系统信息失败:', error)
        this.$message.error('刷新系统信息失败')
      }
    },

    async loadOperationLogs() {
      try {
        // const response = await this.$http.get('/admin/operation-logs?size=10')
        // this.operationLogs = response.data.records
        
        // 模拟数据
        this.operationLogs = [
          {
            id: 1,
            adminName: 'admin',
            operation: '用户管理',
            target: '禁用用户 user001',
            result: 'success',
            createTime: '2023-12-01 10:30:00'
          },
          {
            id: 2,
            adminName: 'admin',
            operation: '积分管理',
            target: '为用户 user002 增加100积分',
            result: 'success',
            createTime: '2023-12-01 10:15:00'
          },
          {
            id: 3,
            adminName: 'admin',
            operation: '系统设置',
            target: '修改语音合成消耗积分为10',
            result: 'success',
            createTime: '2023-12-01 09:45:00'
          }
        ]
      } catch (error) {
        console.error('加载操作日志失败:', error)
      }
    }
  },
  mounted() {
    this.loadSettings()
    this.loadOperationLogs()
  }
}
</script>

<style scoped>
.system-settings {
  padding: 20px;
}
</style>
