/**
 * API统一管理
 * 将所有API请求集中管理，便于维护和复用
 */
import axios from 'axios'
import Cookies from 'js-cookie'
import { Message } from 'element-ui'
import router from '@/router'
import { API_CONFIG, HTTP_STATUS, ERROR_MESSAGES } from './config'

// 创建axios实例
const request = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    const token = Cookies.get('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    const res = response.data
    if (res.code !== HTTP_STATUS.SUCCESS) {
      Message.error(res.message || ERROR_MESSAGES.SERVER_ERROR)
      if (res.code === HTTP_STATUS.UNAUTHORIZED) {
        Cookies.remove('token')
        Cookies.remove('user')
        router.push('/login')
      }
      return Promise.reject(new Error(res.message || ERROR_MESSAGES.SERVER_ERROR))
    }
    return res
  },
  error => {
    let errorMessage = ERROR_MESSAGES.NETWORK_ERROR

    if (error.code === 'ECONNABORTED') {
      errorMessage = ERROR_MESSAGES.TIMEOUT_ERROR
    } else if (error.response) {
      switch (error.response.status) {
        case HTTP_STATUS.UNAUTHORIZED:
          errorMessage = ERROR_MESSAGES.UNAUTHORIZED
          Cookies.remove('token')
          Cookies.remove('user')
          router.push('/login')
          break
        case HTTP_STATUS.FORBIDDEN:
          errorMessage = ERROR_MESSAGES.FORBIDDEN
          break
        case HTTP_STATUS.NOT_FOUND:
          errorMessage = ERROR_MESSAGES.NOT_FOUND
          break
        case HTTP_STATUS.SERVER_ERROR:
          errorMessage = ERROR_MESSAGES.SERVER_ERROR
          break
        default:
          errorMessage = (error.response.data && error.response.data.message) || ERROR_MESSAGES.SERVER_ERROR
      }
    }

    Message.error(errorMessage)
    return Promise.reject(error)
  }
)

export default request
