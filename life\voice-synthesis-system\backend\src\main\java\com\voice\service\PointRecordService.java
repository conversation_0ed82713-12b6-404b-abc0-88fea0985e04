package com.voice.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voice.entity.PointRecord;
import com.voice.mapper.PointRecordMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 积分记录服务类
 */
@Service
public class PointRecordService extends ServiceImpl<PointRecordMapper, PointRecord> {

    /**
     * 添加积分记录
     */
    public void addPointRecord(Long userId, Integer type, Integer points, String source, 
                              String description, Integer beforePoints, Integer afterPoints) {
        PointRecord record = new PointRecord();
        record.setUserId(userId);
        record.setType(type);
        record.setPoints(points);
        record.setSource(source);
        record.setDescription(description);
        record.setBeforePoints(beforePoints);
        record.setAfterPoints(afterPoints);
        record.setCreateTime(LocalDateTime.now());
        
        this.save(record);
    }

    /**
     * 分页查询用户积分记录
     */
    public IPage<PointRecord> getUserPointRecords(Long userId, int current, int size) {
        Page<PointRecord> page = new Page<>(current, size);
        QueryWrapper<PointRecord> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        wrapper.orderByDesc("create_time");
        
        return this.page(page, wrapper);
    }

    /**
     * 分页查询所有积分记录
     */
    public IPage<PointRecord> getPointRecordPage(int current, int size, Long userId, Integer type) {
        Page<PointRecord> page = new Page<>(current, size);
        QueryWrapper<PointRecord> wrapper = new QueryWrapper<>();

        if (userId != null) {
            wrapper.eq("user_id", userId);
        }

        if (type != null) {
            wrapper.eq("type", type);
        }

        wrapper.orderByDesc("create_time");
        return this.page(page, wrapper);
    }

    /**
     * 获取积分统计信息
     */
    public Map<String, Object> getPointsStats() {
        Map<String, Object> stats = new HashMap<>();

        // 总积分消耗
        QueryWrapper<PointRecord> consumeWrapper = new QueryWrapper<>();
        consumeWrapper.eq("type", 2); // 消耗类型
        consumeWrapper.select("IFNULL(SUM(points), 0) as total");
        // 这里需要使用原生SQL查询，暂时使用模拟数据
        stats.put("totalPoints", 8640);

        // 总积分获得
        QueryWrapper<PointRecord> gainWrapper = new QueryWrapper<>();
        gainWrapper.eq("type", 1); // 获得类型
        gainWrapper.select("IFNULL(SUM(points), 0) as total");
        // 这里需要使用原生SQL查询，暂时使用模拟数据
        stats.put("totalGain", 12500);

        // 今日消耗
        QueryWrapper<PointRecord> todayWrapper = new QueryWrapper<>();
        todayWrapper.eq("type", 2);
        todayWrapper.ge("create_time", LocalDateTime.now().toLocalDate());
        // 这里需要使用原生SQL查询，暂时使用模拟数据
        stats.put("todayConsume", 150);

        return stats;
    }
}
