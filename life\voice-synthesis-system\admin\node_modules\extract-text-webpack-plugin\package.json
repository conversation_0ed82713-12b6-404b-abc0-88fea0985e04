{"name": "extract-text-webpack-plugin", "version": "4.0.0-beta.0", "author": "<PERSON> @sokra", "description": "Extract text from bundle into a file.", "license": "MIT", "main": "dist/cjs.js", "files": ["dist"], "scripts": {"start": "npm run build -- -w", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "commitlint": "commitlint", "commitmsg": "commitlint -e $GIT_PARAMS", "lint": "eslint --cache src test", "ci:lint:commits": "commitlint --from=${CIRCLE_BRANCH} --to=${CIRCLE_SHA1}", "lint-staged": "lint-staged", "prebuild": "npm run clean", "prepare": "npm run build", "release": "standard-version", "release:ci": "conventional-github-releaser -p angular", "release:validate": "commitlint --from=$(git describe --tags --abbrev=0) --to=$(git rev-parse HEAD)", "security": "nsp check", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "ci:lint": "npm run lint && npm run security", "ci:test": "npm run test -- --runInBand", "ci:coverage": "npm run test:coverage -- --runInBand", "defaults": "webpack-defaults"}, "dependencies": {"async": "^2.4.1", "loader-utils": "^1.1.0", "schema-utils": "^0.4.5", "webpack-sources": "^1.1.0"}, "devDependencies": {"@commitlint/cli": "^5.2.8", "@commitlint/config-angular": "^5.1.1", "@webpack-contrib/eslint-config-webpack": "^2.0.2", "babel-cli": "^6.26.0", "babel-jest": "^22.2.2", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.1", "conventional-github-releaser": "^2.0.0", "cross-env": "^5.1.3", "css-loader": "^0.28.9", "del": "^3.0.0", "del-cli": "^1.1.0", "eslint": "^4.17.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.6.0", "file-loader": "^1.1.6", "husky": "^0.14.3", "jest": "^22.3.0", "lint-staged": "^6.1.0", "memory-fs": "^0.4.1", "nsp": "^3.1.0", "pre-commit": "^1.2.2", "prettier": "^1.10.2", "raw-loader": "^0.5.1", "standard-version": "^4.3.0", "style-loader": "^0.19.1", "webpack": "^4.0.0", "webpack-defaults": "^2.0.0-rc.4"}, "engines": {"node": ">= 6.9.0 || >= 8.9.0"}, "peerDependencies": {"webpack": "^3.0.0 || ^4.0.0"}, "homepage": "https://github.com/webpack-contrib/extract-text-webpack-plugin", "repository": "https://github.com/webpack-contrib/extract-text-webpack-plugin", "bugs": "https://github.com/webpack-contrib/extract-text-webpack-plugin/issues", "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "jest": {"testEnvironment": "node"}}