{"name": "html-webpack-plugin", "version": "4.5.2", "license": "MIT", "description": "Simplifies creation of HTML files to serve your webpack bundles", "author": "<PERSON> <<EMAIL>> (https://github.com/jantimon)", "main": "index.js", "types": "typings.d.ts", "files": ["lib/", "index.js", "default_index.ejs", "typings.d.ts"], "scripts": {"pretest": "semistandard", "posttest": "tsc", "commit": "git-cz", "build-examples": "node examples/build-examples.js", "test": "jest --runInBand --verbose --coverage", "test-watch": "jest --runInBand --watch", "puml": "npx puml generate flow.puml -o flow.png", "release": "standard-version"}, "semistandard": {"ignore": ["examples/*/dist/**/*.*"]}, "devDependencies": {"@types/loader-utils": "1.1.3", "@types/node": "11.13.9", "appcache-webpack-plugin": "1.4.0", "commitizen": "4.0.3", "css-loader": "2.1.1", "cz-conventional-changelog": "2.1.0", "dir-compare": "1.7.2", "file-loader": "3.0.1", "html-loader": "0.5.5", "jest": "24.7.1", "mini-css-extract-plugin": "0.6.0", "pug": "2.0.3", "pug-loader": "2.4.0", "rimraf": "2.6.3", "semistandard": "^13.0.1", "standard-version": "5.0.2", "style-loader": "0.23.1", "typescript": "3.8.3", "webpack": "4.35.2", "webpack-recompilation-simulator": "3.2.0"}, "dependencies": {"@types/html-minifier-terser": "^5.0.0", "@types/tapable": "^1.0.5", "@types/webpack": "^4.41.8", "html-minifier-terser": "^5.0.1", "loader-utils": "^1.2.3", "lodash": "^4.17.20", "pretty-error": "^2.1.1", "tapable": "^1.1.3", "util.promisify": "1.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "keywords": ["webpack", "plugin", "html", "html-webpack-plugin"], "bugs": "https://github.com/jantimon/html-webpack-plugin/issues", "homepage": "https://github.com/jantimon/html-webpack-plugin", "repository": "https://github.com/jantimon/html-webpack-plugin.git", "engines": {"node": ">=6.9"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "jest": {"watchPathIgnorePatterns": ["<rootDir>/dist"], "testEnvironment": "node"}}