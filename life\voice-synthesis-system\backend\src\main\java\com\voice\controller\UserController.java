package com.voice.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.voice.common.Result;
import com.voice.entity.PointRecord;
import com.voice.entity.User;
import com.voice.service.PointRecordService;
import com.voice.service.UserService;
import com.voice.service.SynthesisRecordService;
import com.voice.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private SynthesisRecordService synthesisRecordService;

    @Autowired
    private PointRecordService pointRecordService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 获取用户统计信息
     */
    @GetMapping("/stats")
    public Result<Map<String, Object>> getUserStats(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            
            // 获取用户信息
            User user = userService.getById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 获取合成次数统计
            int synthesisCount = synthesisRecordService.getUserSynthesisCount(userId);

            Map<String, Object> stats = new HashMap<>();
            stats.put("synthesisCount", synthesisCount);
            stats.put("points", user.getPoints());
            stats.put("username", user.getUsername());
            stats.put("nickname", user.getNickname());

            return Result.success("获取成功", stats);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/info")
    public Result<Map<String, Object>> getUserInfo(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            User user = userService.getById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", user.getId());
            userInfo.put("username", user.getUsername());
            userInfo.put("nickname", user.getNickname());
            userInfo.put("email", user.getEmail());
            userInfo.put("phone", user.getPhone());
            userInfo.put("points", user.getPoints());
            userInfo.put("avatar", user.getAvatar());
            userInfo.put("createTime", user.getCreateTime());

            return Result.success("获取成功", userInfo);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取用户积分
     */
    @GetMapping("/points")
    public Result<Map<String, Object>> getUserPoints(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            User user = userService.getById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("points", user.getPoints());

            return Result.success("获取成功", result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取用户积分记录
     */
    @GetMapping("/point-records")
    public Result<Map<String, Object>> getPointRecords(
            HttpServletRequest request,
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Long userId = getUserIdFromToken(request);

            IPage<PointRecord> page = pointRecordService.getUserPointRecords(userId, current, size);

            Map<String, Object> result = new HashMap<>();
            result.put("records", page.getRecords());
            result.put("total", page.getTotal());
            result.put("current", page.getCurrent());
            result.put("size", page.getSize());
            result.put("pages", page.getPages());

            return Result.success("获取成功", result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 从请求中获取用户ID
     */
    private Long getUserIdFromToken(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
            return jwtUtil.getUserIdFromToken(token);
        }
        throw new RuntimeException("未找到有效的token");
    }
}
