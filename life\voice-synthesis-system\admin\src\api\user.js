import request from './request'

// 获取用户列表
export function getUserList(params) {
  return request({
    url: '/admin/users',
    method: 'get',
    params
  })
}

// 获取用户详情
export function getUserDetail(id) {
  return request({
    url: `/admin/users/${id}`,
    method: 'get'
  })
}

// 创建用户
export function createUser(data) {
  return request({
    url: '/admin/users',
    method: 'post',
    data
  })
}

// 更新用户信息
export function updateUser(id, data) {
  return request({
    url: `/admin/users/${id}`,
    method: 'put',
    data
  })
}

// 删除用户
export function deleteUser(id) {
  return request({
    url: `/admin/users/${id}`,
    method: 'delete'
  })
}

// 切换用户状态
export function toggleUserStatus(id, status) {
  return request({
    url: `/admin/users/${id}/status`,
    method: 'patch',
    data: { status }
  })
}

// 重置用户密码
export function resetUserPassword(id, password) {
  return request({
    url: `/admin/users/${id}/password`,
    method: 'patch',
    data: { password }
  })
}

// 获取用户统计信息
export function getUserStats() {
  return request({
    url: '/admin/users/stats',
    method: 'get'
  })
}
