import Vue from 'vue'
import App from './App'
import router from './router'
import store from './store'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import axios from 'axios'
import Cookies from 'js-cookie'
// 导入环境变量测试
import './utils/env-test'
// 简化ECharts导入，避免版本兼容问题
// import ECharts from 'vue-echarts'
// import 'echarts/lib/chart/line'
// import 'echarts/lib/chart/bar'
// import 'echarts/lib/chart/pie'
// import 'echarts/lib/component/tooltip'
// import 'echarts/lib/component/legend'
// import 'echarts/lib/component/title'

Vue.config.productionTip = false

// 使用Element UI
Vue.use(ElementUI)

// 注册ECharts组件 - 暂时注释掉避免兼容性问题
// Vue.component('v-chart', ECharts)

// 配置axios
axios.defaults.baseURL = process.env.VUE_APP_BASE_API || 'http://localhost:8080'
axios.defaults.timeout = 7200000  // 2小时超时

// 请求拦截器
axios.interceptors.request.use(
  config => {
    const token = Cookies.get('admin_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
axios.interceptors.response.use(
  response => {
    const res = response.data
    if (res.code !== 200) {
      ElementUI.Message.error(res.message || '请求失败')
      if (res.code === 401) {
        Cookies.remove('admin_token')
        Cookies.remove('admin_user')
        router.push('/login')
      }
      return Promise.reject(new Error(res.message || '请求失败'))
    }
    return res
  },
  error => {
    ElementUI.Message.error(error.message || '网络错误')
    return Promise.reject(error)
  }
)

Vue.prototype.$http = axios

new Vue({
  el: '#app',
  router,
  store,
  components: { App },
  template: '<App/>'
})
