import request from './request'

// 管理员登录
export function adminLogin(data) {
  return request({
    url: '/admin/login',
    method: 'post',
    data
  })
}

// 管理员登出
export function adminLogout() {
  return request({
    url: '/admin/logout',
    method: 'post'
  })
}

// 获取管理员信息
export function getAdminInfo() {
  return request({
    url: '/admin/info',
    method: 'get'
  })
}

// 修改管理员密码
export function changeAdminPassword(data) {
  return request({
    url: '/admin/change-password',
    method: 'post',
    data
  })
}

// 刷新token
export function refreshToken() {
  return request({
    url: '/admin/refresh-token',
    method: 'post'
  })
}
