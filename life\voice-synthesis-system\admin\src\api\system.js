import request from './request'

// 获取系统配置
export function getSystemConfig() {
  return request({
    url: '/admin/system/config',
    method: 'get'
  })
}

// 更新系统配置
export function updateSystemConfig(data) {
  return request({
    url: '/admin/system/config',
    method: 'put',
    data
  })
}

// 获取系统状态
export function getSystemStatus() {
  return request({
    url: '/admin/system/status',
    method: 'get'
  })
}

// 获取系统日志
export function getSystemLogs(params) {
  return request({
    url: '/admin/system/logs',
    method: 'get',
    params
  })
}

// 清理系统缓存
export function clearSystemCache() {
  return request({
    url: '/admin/system/clear-cache',
    method: 'post'
  })
}

// 备份数据库
export function backupDatabase() {
  return request({
    url: '/admin/system/backup',
    method: 'post'
  })
}

// 获取备份列表
export function getBackupList() {
  return request({
    url: '/admin/system/backups',
    method: 'get'
  })
}

// 恢复数据库
export function restoreDatabase(backupId) {
  return request({
    url: `/admin/system/restore/${backupId}`,
    method: 'post'
  })
}
