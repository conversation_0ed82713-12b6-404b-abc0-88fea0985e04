package com.voice.controller;

import com.voice.common.Result;
import com.voice.dto.AdminLoginDTO;
import com.voice.entity.Admin;
import com.voice.service.AdminService;
import com.voice.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 管理员控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/admin")
@CrossOrigin
public class AdminController {

    @Autowired
    private AdminService adminService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 管理员登录
     */
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@RequestBody AdminLoginDTO loginDTO) {
        try {
            log.info("管理员登录请求: {}", loginDTO.getUsername());
            
            Admin admin = adminService.login(loginDTO.getUsername(), loginDTO.getPassword());
            if (admin == null) {
                return Result.error("用户名或密码错误");
            }

            // 生成JWT token
            String token = jwtUtil.generateToken(admin.getUsername(), "admin", admin.getId());
            
            Map<String, Object> data = new HashMap<>();
            data.put("token", token);
            data.put("admin", admin);
            
            return Result.success(data);
        } catch (Exception e) {
            log.error("管理员登录失败", e);
            return Result.error("登录失败: " + e.getMessage());
        }
    }

    /**
     * 获取管理员信息
     */
    @GetMapping("/info")
    public Result<Admin> getAdminInfo(HttpServletRequest request) {
        try {
            String token = request.getHeader("Authorization");
            if (token != null && token.startsWith("Bearer ")) {
                token = token.substring(7);
            }

            if (token == null) {
                return Result.error("缺少token");
            }

            // 先获取用户名，然后验证token
            String username = jwtUtil.getUsernameFromToken(token);
            if (username == null || !jwtUtil.validateToken(token, username)) {
                return Result.error("无效的token");
            }

            Long adminId = jwtUtil.getUserIdFromToken(token);
            Admin admin = adminService.getById(adminId);

            if (admin == null) {
                return Result.error("管理员不存在");
            }

            // 清除密码信息
            admin.setPassword(null);

            return Result.success(admin);
        } catch (Exception e) {
            log.error("获取管理员信息失败", e);
            return Result.error("获取管理员信息失败: " + e.getMessage());
        }
    }

    /**
     * 管理员登出
     */
    @PostMapping("/logout")
    public Result<String> logout() {
        // 这里可以实现token黑名单机制
        return Result.success("登出成功");
    }

    /**
     * 修改管理员密码
     */
    @PostMapping("/change-password")
    public Result<String> changePassword(@RequestBody Map<String, String> request, HttpServletRequest httpRequest) {
        try {
            String token = httpRequest.getHeader("Authorization");
            if (token != null && token.startsWith("Bearer ")) {
                token = token.substring(7);
            }

            if (token == null) {
                return Result.error("缺少token");
            }

            // 先获取用户名，然后验证token
            String username = jwtUtil.getUsernameFromToken(token);
            if (username == null || !jwtUtil.validateToken(token, username)) {
                return Result.error("无效的token");
            }

            Long adminId = jwtUtil.getUserIdFromToken(token);
            String oldPassword = request.get("oldPassword");
            String newPassword = request.get("newPassword");

            boolean success = adminService.changePassword(adminId, oldPassword, newPassword);

            if (success) {
                return Result.success("密码修改成功");
            } else {
                return Result.error("原密码错误");
            }
        } catch (Exception e) {
            log.error("修改密码失败", e);
            return Result.error("修改密码失败: " + e.getMessage());
        }
    }

    /**
     * 刷新token
     */
    @PostMapping("/refresh-token")
    public Result<Map<String, String>> refreshToken(HttpServletRequest request) {
        try {
            String token = request.getHeader("Authorization");
            if (token != null && token.startsWith("Bearer ")) {
                token = token.substring(7);
            }

            if (token == null) {
                return Result.error("缺少token");
            }

            // 先获取用户名，然后验证token
            String username = jwtUtil.getUsernameFromToken(token);
            if (username == null || !jwtUtil.validateToken(token, username)) {
                return Result.error("无效的token");
            }

            Long adminId = jwtUtil.getUserIdFromToken(token);
            String newToken = jwtUtil.generateToken(username, "admin", adminId);

            Map<String, String> data = new HashMap<>();
            data.put("token", newToken);

            return Result.success(data);
        } catch (Exception e) {
            log.error("刷新token失败", e);
            return Result.error("刷新token失败: " + e.getMessage());
        }
    }
}
