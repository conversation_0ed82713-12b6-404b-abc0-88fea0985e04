package com.voice.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.voice.common.Result;
import com.voice.entity.User;
import com.voice.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 管理员用户管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin")
@CrossOrigin
public class AdminUserController {

    @Autowired
    private UserService userService;

    /**
     * 获取用户列表
     */
    @GetMapping("/users")
    public Result<Map<String, Object>> getUserList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword) {
        try {
            Page<User> pageParam = new Page<>(page, size);
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            
            if (StringUtils.hasText(keyword)) {
                queryWrapper.and(wrapper -> wrapper
                    .like("username", keyword)
                    .or()
                    .like("nickname", keyword)
                    .or()
                    .like("email", keyword));
            }
            
            queryWrapper.orderByDesc("create_time");
            
            IPage<User> userPage = userService.page(pageParam, queryWrapper);
            
            // 清除密码信息
            userPage.getRecords().forEach(user -> user.setPassword(null));
            
            Map<String, Object> data = new HashMap<>();
            data.put("records", userPage.getRecords());
            data.put("total", userPage.getTotal());
            data.put("pages", userPage.getPages());
            data.put("current", userPage.getCurrent());
            data.put("size", userPage.getSize());
            
            return Result.success(data);
        } catch (Exception e) {
            log.error("获取用户列表失败", e);
            return Result.error("获取用户列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户详情
     */
    @GetMapping("/users/{id}")
    public Result<User> getUserDetail(@PathVariable Long id) {
        try {
            User user = userService.getById(id);
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            // 清除密码信息
            user.setPassword(null);
            
            return Result.success(user);
        } catch (Exception e) {
            log.error("获取用户详情失败", e);
            return Result.error("获取用户详情失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/users/{id}")
    public Result<String> updateUser(@PathVariable Long id, @RequestBody User user) {
        try {
            User existingUser = userService.getById(id);
            if (existingUser == null) {
                return Result.error("用户不存在");
            }
            
            user.setId(id);
            // 不允许通过此接口修改密码
            user.setPassword(null);
            
            boolean success = userService.updateById(user);
            
            if (success) {
                return Result.success("用户信息更新成功");
            } else {
                return Result.error("用户信息更新失败");
            }
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            return Result.error("更新用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 切换用户状态
     */
    @PatchMapping("/users/{id}/status")
    public Result<String> toggleUserStatus(@PathVariable Long id, @RequestBody Map<String, Integer> request) {
        try {
            User user = userService.getById(id);
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            Integer status = request.get("status");
            user.setStatus(status);
            
            boolean success = userService.updateById(user);
            
            if (success) {
                String action = status == 1 ? "启用" : "禁用";
                return Result.success(action + "用户成功");
            } else {
                return Result.error("操作失败");
            }
        } catch (Exception e) {
            log.error("切换用户状态失败", e);
            return Result.error("切换用户状态失败: " + e.getMessage());
        }
    }

    /**
     * 重置用户密码
     */
    @PatchMapping("/users/{id}/password")
    public Result<String> resetUserPassword(@PathVariable Long id, @RequestBody Map<String, String> request) {
        try {
            String newPassword = request.get("password");
            boolean success = userService.resetPassword(id, newPassword);
            
            if (success) {
                return Result.success("密码重置成功");
            } else {
                return Result.error("密码重置失败");
            }
        } catch (Exception e) {
            log.error("重置用户密码失败", e);
            return Result.error("重置用户密码失败: " + e.getMessage());
        }
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/users/{id}")
    public Result<String> deleteUser(@PathVariable Long id) {
        try {
            boolean success = userService.removeById(id);
            
            if (success) {
                return Result.success("删除用户成功");
            } else {
                return Result.error("删除用户失败");
            }
        } catch (Exception e) {
            log.error("删除用户失败", e);
            return Result.error("删除用户失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户统计信息
     */
    @GetMapping("/users/stats")
    public Result<Map<String, Object>> getUserStats() {
        try {
            Map<String, Object> stats = userService.getUserStats();
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取用户统计信息失败", e);
            return Result.error("获取用户统计信息失败: " + e.getMessage());
        }
    }
}
