# Lower Case

[![NPM version][npm-image]][npm-url]
[![NPM downloads][downloads-image]][downloads-url]
[![Bundle size][bundlephobia-image]][bundlephobia-url]

> Transforms the string to lower case.

## Installation

```
npm install lower-case --save
```

## Usage

```js
import { lowerCase, localeLowerCase } from "lower-case";

lowerCase("string"); //=> "string"
lowerCase("PascalCase"); //=> "pascalcase"

localeLowerCase("STRING", "tr"); //=> "strıng"
```

## License

MIT

[npm-image]: https://img.shields.io/npm/v/lower-case.svg?style=flat
[npm-url]: https://npmjs.org/package/lower-case
[downloads-image]: https://img.shields.io/npm/dm/lower-case.svg?style=flat
[downloads-url]: https://npmjs.org/package/lower-case
[bundlephobia-image]: https://img.shields.io/bundlephobia/minzip/lower-case.svg
[bundlephobia-url]: https://bundlephobia.com/result?p=lower-case
