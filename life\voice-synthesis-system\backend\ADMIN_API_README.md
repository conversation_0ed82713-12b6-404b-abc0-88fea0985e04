# 管理员API文档

## 概述

本文档描述了语音合成系统管理员后台的API接口。所有管理员API都需要通过JWT token进行身份验证。

## 基础信息

- 基础URL: `http://localhost:8080/api`
- 认证方式: Bearer <PERSON>ken (JWT)
- 数据格式: JSON

## 默认管理员账号

- 用户名: `admin`
- 密码: `admin123`

## API接口

### 1. 管理员认证

#### 1.1 管理员登录
```
POST /admin/login
```

请求体:
```json
{
  "username": "admin",
  "password": "admin123"
}
```

响应:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiJ9...",
    "admin": {
      "id": 1,
      "username": "admin",
      "realName": "系统管理员",
      "email": "<EMAIL>",
      "role": "super",
      "status": 1
    }
  }
}
```

#### 1.2 获取管理员信息
```
GET /admin/info
Headers: Authorization: Bearer {token}
```

#### 1.3 管理员登出
```
POST /admin/logout
Headers: Authorization: Bearer {token}
```

### 2. 用户管理

#### 2.1 获取用户列表
```
GET /admin/users?page=1&size=20&keyword=搜索关键词
Headers: Authorization: Bearer {token}
```

#### 2.2 获取用户详情
```
GET /admin/users/{id}
Headers: Authorization: Bearer {token}
```

#### 2.3 更新用户信息
```
PUT /admin/users/{id}
Headers: Authorization: Bearer {token}
Content-Type: application/json

{
  "nickname": "新昵称",
  "email": "<EMAIL>",
  "status": 1
}
```

#### 2.4 切换用户状态
```
PATCH /admin/users/{id}/status
Headers: Authorization: Bearer {token}
Content-Type: application/json

{
  "status": 0
}
```

### 3. 积分管理

#### 3.1 获取积分记录列表
```
GET /admin/point-records?page=1&size=20&type=1&userId=1
Headers: Authorization: Bearer {token}
```

#### 3.2 获取积分统计信息
```
GET /admin/points/stats
Headers: Authorization: Bearer {token}
```

#### 3.3 手动调整用户积分
```
POST /admin/points/adjust
Headers: Authorization: Bearer {token}
Content-Type: application/json

{
  "userId": 1,
  "type": "add",
  "points": 100,
  "description": "管理员手动调整"
}
```

### 4. 合成管理

#### 4.1 获取合成记录列表
```
GET /admin/synthesis-records?page=1&size=20&status=1
Headers: Authorization: Bearer {token}
```

#### 4.2 获取合成统计信息
```
GET /admin/synthesis/stats
Headers: Authorization: Bearer {token}
```

#### 4.3 删除合成记录
```
DELETE /admin/synthesis-records/{id}
Headers: Authorization: Bearer {token}
```

### 5. 系统设置

#### 5.1 获取系统配置
```
GET /admin/system/config
Headers: Authorization: Bearer {token}
```

#### 5.2 更新系统配置
```
PUT /admin/system/config
Headers: Authorization: Bearer {token}
Content-Type: application/json

{
  "synthesisCost": 10,
  "registerPoints": 100,
  "gptSovitsApiUrl": "http://127.0.0.1:9880",
  "maxTextLength": 500,
  "audioStoragePath": "/uploads/audio/"
}
```

#### 5.3 获取系统状态
```
GET /admin/system/status
Headers: Authorization: Bearer {token}
```

### 6. 仪表板

#### 6.1 获取仪表板统计数据
```
GET /admin/dashboard/stats
Headers: Authorization: Bearer {token}
```

#### 6.2 获取最近活动
```
GET /admin/dashboard/recent-activities?limit=10
Headers: Authorization: Bearer {token}
```

## 错误码说明

- 200: 成功
- 400: 请求参数错误
- 401: 未授权或token无效
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器内部错误

## 测试

运行测试脚本:
```bash
cd test
node admin-api-test.js
```

## 注意事项

1. 所有管理员API都需要在请求头中携带有效的JWT token
2. Token格式: `Authorization: Bearer {token}`
3. 默认管理员账号密码请在生产环境中及时修改
4. API返回的时间格式为: `YYYY-MM-DD HH:mm:ss`
