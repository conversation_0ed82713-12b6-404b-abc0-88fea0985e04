package com.voice.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * 简单HTTP客户端配置
 * 使用Spring Boot内置的SimpleClientHttpRequestFactory
 * 不需要额外的Apache HttpClient依赖
 */
@Configuration
public class SimpleHttpClientConfig {

    @Bean(name = "simpleTimeoutRestTemplate")
    public RestTemplate simpleTimeoutRestTemplate() {
        // 2小时超时配置
        int timeout = 7200000; // 2小时 = 7200秒 = 7200000毫秒
        
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(timeout);  // 连接超时
        factory.setReadTimeout(timeout);     // 读取超时
        
        return new RestTemplate(factory);
    }
}
