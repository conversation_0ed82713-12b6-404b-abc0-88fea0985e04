{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;AAAA,mCAA0C;AAI1C,SAAgB,mBAAmB,CAAC,KAAa,EAAE,KAAa;IAC9D,IAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAClC,IAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;IACjD,IAAI,KAAK,GAAG,CAAC,IAAI,SAAS,IAAI,GAAG,IAAI,SAAS,IAAI,GAAG,EAAE;QACrD,OAAO,MAAI,SAAS,GAAG,UAAY,CAAC;KACrC;IACD,OAAO,KAAG,SAAS,CAAC,WAAW,EAAE,GAAG,UAAY,CAAC;AACnD,CAAC;AAPD,kDAOC;AAED,SAAgB,wBAAwB,CAAC,KAAa;IACpD,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;AACtE,CAAC;AAFD,4DAEC;AAED,SAAgB,UAAU,CAAC,KAAa,EAAE,OAAqB;IAArB,wBAAA,EAAA,YAAqB;IAC7D,OAAO,gBAAM,CAAC,KAAK,qBACjB,SAAS,EAAE,EAAE,EACb,SAAS,EAAE,mBAAmB,IAC3B,OAAO,EACV,CAAC;AACL,CAAC;AAND,gCAMC", "sourcesContent": ["import { noCase, Options } from \"no-case\";\n\nexport { Options };\n\nexport function pascalCaseTransform(input: string, index: number) {\n  const firstChar = input.charAt(0);\n  const lowerChars = input.substr(1).toLowerCase();\n  if (index > 0 && firstChar >= \"0\" && firstChar <= \"9\") {\n    return `_${firstChar}${lowerChars}`;\n  }\n  return `${firstChar.toUpperCase()}${lowerChars}`;\n}\n\nexport function pascalCaseTransformMerge(input: string) {\n  return input.charAt(0).toUpperCase() + input.slice(1).toLowerCase();\n}\n\nexport function pascalCase(input: string, options: Options = {}) {\n  return noCase(input, {\n    delimiter: \"\",\n    transform: pascalCaseTransform,\n    ...options,\n  });\n}\n"]}