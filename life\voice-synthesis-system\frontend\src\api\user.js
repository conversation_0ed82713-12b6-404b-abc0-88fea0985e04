/**
 * 用户相关API
 */
import request from './index'
import { API_ENDPOINTS } from './config'

// 获取用户统计信息
export function getUserStats() {
  return request({
    url: API_ENDPOINTS.USER.STATS,
    method: 'get'
  })
}

// 更新用户资料
export function updateUserProfile(data) {
  return request({
    url: API_ENDPOINTS.USER.PROFILE,
    method: 'put',
    data
  })
}

// 修改密码
export function changePassword(data) {
  return request({
    url: API_ENDPOINTS.USER.PASSWORD,
    method: 'put',
    data
  })
}

// 获取积分记录
export function getPointRecords(params) {
  return request({
    url: API_ENDPOINTS.USER.POINT_RECORDS,
    method: 'get',
    params
  })
}

// 获取用户积分余额
export function getUserPoints() {
  return request({
    url: API_ENDPOINTS.USER.POINTS,
    method: 'get'
  })
}

// 充值积分
export function rechargePoints(data) {
  return request({
    url: API_ENDPOINTS.USER.RECHARGE,
    method: 'post',
    data
  })
}

// 上传头像
export function uploadAvatar(formData) {
  return request({
    url: API_ENDPOINTS.USER.AVATAR,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
