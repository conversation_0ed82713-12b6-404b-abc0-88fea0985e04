<template>
  <div class="synthesis-management">
    <el-card>
      <div slot="header" class="card-header">
        <span>合成记录管理</span>
        <div class="header-actions">
          <el-select v-model="filterStatus" placeholder="状态筛选" style="width: 120px; margin-right: 10px;">
            <el-option label="全部" value=""></el-option>
            <el-option label="成功" :value="1"></el-option>
            <el-option label="失败" :value="0"></el-option>
            <el-option label="处理中" :value="2"></el-option>
          </el-select>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </div>
      </div>

      <!-- 合成记录列表 -->
      <el-table :data="records" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="username" label="用户" width="120"></el-table-column>
        <el-table-column prop="text" label="合成文本" width="200" show-overflow-tooltip></el-table-column>
        <el-table-column prop="modelName" label="模型" width="120"></el-table-column>
        <el-table-column prop="pointsCost" label="消耗积分" width="100"></el-table-column>
        <el-table-column prop="fileSize" label="文件大小" width="100">
          <template slot-scope="scope">
            {{ scope.row.fileSize ? formatFileSize(scope.row.fileSize) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : scope.row.status === 0 ? 'danger' : 'warning'" size="mini">
              {{ scope.row.status === 1 ? '成功' : scope.row.status === 0 ? '失败' : '处理中' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180"></el-table-column>
        <el-table-column label="操作" width="150">
          <template slot-scope="scope">
            <el-button type="text" @click="viewRecord(scope.row)">查看</el-button>
            <el-button type="text" class="danger-text" @click="deleteRecord(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog title="合成记录详情" :visible.sync="detailDialogVisible" width="600px">
      <div v-if="currentRecord">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="记录ID">{{ currentRecord.id }}</el-descriptions-item>
          <el-descriptions-item label="用户">{{ currentRecord.username }}</el-descriptions-item>
          <el-descriptions-item label="合成文本" :span="2">{{ currentRecord.text }}</el-descriptions-item>
          <el-descriptions-item label="模型名称">{{ currentRecord.modelName }}</el-descriptions-item>
          <el-descriptions-item label="消耗积分">{{ currentRecord.pointsCost }}</el-descriptions-item>
          <el-descriptions-item label="文件大小">
            {{ currentRecord.fileSize ? formatFileSize(currentRecord.fileSize) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentRecord.status === 1 ? 'success' : currentRecord.status === 0 ? 'danger' : 'warning'">
              {{ currentRecord.status === 1 ? '成功' : currentRecord.status === 0 ? '失败' : '处理中' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">{{ currentRecord.createTime }}</el-descriptions-item>
          <el-descriptions-item v-if="currentRecord.errorMessage" label="错误信息" :span="2">
            {{ currentRecord.errorMessage }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div v-if="currentRecord.audioUrl" style="margin-top: 20px;">
          <h4>音频文件</h4>
          <audio controls :src="getAudioUrl(currentRecord.audioUrl)" style="width: 100%; margin-top: 10px;"></audio>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSynthesisRecords, deleteSynthesisRecord } from '@/api/synthesis'

export default {
  name: 'SynthesisManagement',
  data() {
    return {
      records: [],
      loading: false,
      filterStatus: '',
      currentPage: 1,
      pageSize: 20,
      total: 0,
      detailDialogVisible: false,
      currentRecord: null
    }
  },
  methods: {
    async loadRecords() {
      this.loading = true
      try {
        const response = await getSynthesisRecords({
          page: this.currentPage,
          size: this.pageSize,
          status: this.filterStatus
        })
        this.records = response.data.records || []
        this.total = response.data.total || 0
      } catch (error) {
        console.error('加载合成记录失败:', error)
        this.$message.error('加载合成记录失败')
        // 如果API调用失败，使用模拟数据作为后备
        this.records = [
          {
            id: 1,
            username: 'user001',
            text: '这是一个测试文本，用于语音合成',
            modelName: 'zhexue_lao',
            pointsCost: 10,
            fileSize: 1024000,
            status: 1,
            audioUrl: '/audio/test1.wav',
            createTime: '2023-12-01 10:30:00'
          },
          {
            id: 2,
            username: 'user002',
            text: '另一个语音合成测试',
            modelName: 'xiaoshuo_nan',
            pointsCost: 10,
            fileSize: 2048000,
            status: 1,
            audioUrl: '/audio/test2.wav',
            createTime: '2023-12-01 09:15:00'
          },
          {
            id: 3,
            username: 'user003',
            text: '失败的合成尝试',
            modelName: 'zhexue_lao',
            pointsCost: 10,
            status: 0,
            errorMessage: '模型加载失败',
            createTime: '2023-11-30 16:45:00'
          }
        ]
        this.total = 3
      } finally {
        this.loading = false
      }
    },

    handleSearch() {
      this.currentPage = 1
      this.loadRecords()
    },

    resetSearch() {
      this.filterStatus = ''
      this.currentPage = 1
      this.loadRecords()
    },

    handleSizeChange(val) {
      this.pageSize = val
      this.loadRecords()
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.loadRecords()
    },

    viewRecord(record) {
      this.currentRecord = record
      this.detailDialogVisible = true
    },

    async deleteRecord(record) {
      this.$confirm(`确定要删除合成记录 ${record.id} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteSynthesisRecord(record.id)
          this.$message.success('删除成功')
          this.loadRecords()
        } catch (error) {
          console.error('删除合成记录失败:', error)
          this.$message.error('删除合成记录失败')
        }
      })
    },

    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    getAudioUrl(audioUrl) {
      return `http://localhost:8080/api${audioUrl}`
    }
  },
  mounted() {
    this.loadRecords()
  }
}
</script>

<style scoped>
.synthesis-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.danger-text {
  color: #F56C6C;
}
</style>
