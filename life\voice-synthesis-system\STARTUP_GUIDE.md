# 语音合成系统启动指南

## 问题解决总结

### 1. 左侧导航栏显示不全问题 ✅ 已解决
- 将侧边栏宽度从 200px 增加到 240px
- 优化了菜单项的CSS样式和间距
- 添加了正确的slot属性确保文本显示

### 2. 模拟数据替换为真实API ✅ 已解决
- 创建了完整的后端API控制器
- 前端所有页面都已连接到真实API
- 添加了错误处理和后备机制

### 3. 后端API 404错误 ✅ 已解决
- 创建了6个管理员控制器处理所有API请求
- 添加了必要的服务类和数据访问层
- 配置了JWT认证和权限控制

### 4. 环境变量配置问题 ✅ 已解决
- 在dev.env.js和prod.env.js中添加了VUE_APP_BASE_API
- 修复了main.js中硬编码的API地址
- 添加了环境变量测试工具

## 启动步骤

### 1. 启动后端服务
```bash
cd backend
mvn spring-boot:run
```
后端将在 http://localhost:8080 启动

### 2. 初始化数据库
确保MySQL数据库运行，然后执行：
```sql
-- 运行 database/init.sql 脚本
```

默认管理员账号：
- 用户名: admin
- 密码: admin123

### 3. 启动前端服务
```bash
cd admin
npm install  # 如果还没安装依赖
npm run dev
```
前端将在 http://localhost:8081 启动

### 4. 验证系统运行

#### 方法1: 使用API测试页面
1. 访问 http://localhost:8081
2. 使用默认管理员账号登录
3. 在左侧导航栏点击"API测试"
4. 点击各个测试按钮验证API连接

#### 方法2: 使用测试脚本
```bash
cd test
node admin-api-test.js
```

#### 方法3: 手动验证
1. 登录管理后台
2. 检查各个页面是否正常加载数据
3. 查看浏览器控制台是否有错误

## 环境变量配置

### 开发环境 (dev.env.js)
```javascript
VUE_APP_BASE_API: '"http://localhost:8080"'
```

### 生产环境 (prod.env.js)
```javascript
VUE_APP_BASE_API: '"http://your-production-server:8080"'
```

## API端点列表

### 认证相关
- POST /api/admin/login - 管理员登录
- GET /api/admin/info - 获取管理员信息
- POST /api/admin/logout - 管理员登出

### 用户管理
- GET /api/admin/users - 获取用户列表
- GET /api/admin/users/{id} - 获取用户详情
- PUT /api/admin/users/{id} - 更新用户信息
- PATCH /api/admin/users/{id}/status - 切换用户状态

### 积分管理
- GET /api/admin/point-records - 获取积分记录
- GET /api/admin/points/stats - 获取积分统计
- POST /api/admin/points/adjust - 调整用户积分

### 合成管理
- GET /api/admin/synthesis-records - 获取合成记录
- GET /api/admin/synthesis/stats - 获取合成统计
- DELETE /api/admin/synthesis-records/{id} - 删除合成记录

### 系统设置
- GET /api/admin/system/config - 获取系统配置
- PUT /api/admin/system/config - 更新系统配置
- GET /api/admin/system/status - 获取系统状态

### 仪表板
- GET /api/admin/dashboard/stats - 获取统计数据
- GET /api/admin/dashboard/recent-activities - 获取最近活动

## 故障排除

### 1. 环境变量未生效
- 重启开发服务器: `npm run dev`
- 检查浏览器控制台的环境变量输出
- 访问API测试页面查看实际配置

### 2. API连接失败
- 确保后端服务正在运行
- 检查防火墙设置
- 验证数据库连接

### 3. 登录失败
- 确保数据库已初始化
- 检查默认管理员账号是否存在
- 查看后端日志

### 4. 页面显示异常
- 清除浏览器缓存
- 检查控制台错误信息
- 验证路由配置

## 开发建议

1. **使用API测试页面**进行快速调试
2. **查看浏览器控制台**了解环境变量和错误信息
3. **检查后端日志**排查API问题
4. **使用开发者工具**监控网络请求

## 生产部署注意事项

1. 修改生产环境的API地址
2. 更改默认管理员密码
3. 配置HTTPS和安全头
4. 设置适当的CORS策略
5. 配置日志记录和监控
