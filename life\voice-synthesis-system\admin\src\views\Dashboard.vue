<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon user-icon">
              <i class="el-icon-user"></i>
            </div>
            <div class="stat-content">
              <h3>{{ stats.totalUsers }}</h3>
              <p>总用户数</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon synthesis-icon">
              <i class="el-icon-microphone"></i>
            </div>
            <div class="stat-content">
              <h3>{{ stats.totalSynthesis }}</h3>
              <p>总合成次数</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon points-icon">
              <i class="el-icon-coin"></i>
            </div>
            <div class="stat-content">
              <h3>{{ stats.totalPoints }}</h3>
              <p>总积分消耗</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon today-icon">
              <i class="el-icon-date"></i>
            </div>
            <div class="stat-content">
              <h3>{{ stats.todaySynthesis }}</h3>
              <p>今日合成</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card>
          <div slot="header">
            <span>用户注册趋势</span>
          </div>
          <div class="chart-container">
            <!-- <v-chart :option="userTrendOption" style="height: 300px;"></v-chart> -->
            <div style="height: 300px; display: flex; align-items: center; justify-content: center; background: #f5f5f5; border-radius: 4px;">
              <span style="color: #999;">图表组件加载中...</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <div slot="header">
            <span>语音合成统计</span>
          </div>
          <div class="chart-container">
            <!-- <v-chart :option="synthesisOption" style="height: 300px;"></v-chart> -->
            <div style="height: 300px; display: flex; align-items: center; justify-content: center; background: #f5f5f5; border-radius: 4px;">
              <span style="color: #999;">图表组件加载中...</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近活动 -->
    <el-row :gutter="20" class="activity-row">
      <el-col :span="12">
        <el-card>
          <div slot="header">
            <span>最近注册用户</span>
          </div>
          <el-table :data="recentUsers" style="width: 100%">
            <el-table-column prop="username" label="用户名" width="120"></el-table-column>
            <el-table-column prop="nickname" label="昵称" width="120"></el-table-column>
            <el-table-column prop="points" label="积分" width="80"></el-table-column>
            <el-table-column prop="createTime" label="注册时间"></el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <div slot="header">
            <span>最近合成记录</span>
          </div>
          <el-table :data="recentSynthesis" style="width: 100%">
            <el-table-column prop="username" label="用户" width="100"></el-table-column>
            <el-table-column prop="text" label="文本" show-overflow-tooltip></el-table-column>
            <el-table-column prop="modelName" label="模型" width="100"></el-table-column>
            <el-table-column prop="status" label="状态" width="80">
              <template slot-scope="scope">
                <el-tag :type="scope.row.status === 1 ? 'success' : scope.row.status === 0 ? 'danger' : 'warning'" size="mini">
                  {{ scope.row.status === 1 ? '成功' : scope.row.status === 0 ? '失败' : '处理中' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
// import VChart from 'vue-echarts'
import { getDashboardStats, getUserGrowthTrend, getSynthesisUsageTrend, getRecentActivities } from '@/api/dashboard'

export default {
  name: 'Dashboard',
  // components: {
  //   VChart
  // },
  data() {
    return {
      stats: {
        totalUsers: 0,
        totalSynthesis: 0,
        totalPoints: 0,
        todaySynthesis: 0
      },
      recentUsers: [],
      recentSynthesis: [],
      userTrendOption: {
        title: {
          text: '用户注册趋势'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          data: [],
          type: 'line',
          smooth: true
        }]
      },
      synthesisOption: {
        title: {
          text: '语音合成统计'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          data: [],
          type: 'bar'
        }]
      }
    }
  },
  methods: {
    async loadDashboardData() {
      try {
        // 加载统计数据
        const statsResponse = await getDashboardStats()
        this.stats = statsResponse.data || {
          totalUsers: 0,
          totalSynthesis: 0,
          totalPoints: 0,
          todaySynthesis: 0
        }

        // 加载最近活动数据
        const activitiesResponse = await getRecentActivities()
        const activities = activitiesResponse.data || {}

        this.recentUsers = activities.recentUsers || []
        this.recentSynthesis = activities.recentSynthesis || []

        // 更新图表数据
        this.updateCharts()
      } catch (error) {
        console.error('加载仪表板数据失败:', error)
        this.$message.error('加载仪表板数据失败')

        // 如果API调用失败，使用模拟数据作为后备
        this.stats = {
          totalUsers: 1250,
          totalSynthesis: 8640,
          totalPoints: 86400,
          todaySynthesis: 156
        }

        this.recentUsers = [
          { username: 'user001', nickname: '张三', points: 100, createTime: '2023-12-01 10:30:00' },
          { username: 'user002', nickname: '李四', points: 100, createTime: '2023-12-01 09:15:00' },
          { username: 'user003', nickname: '王五', points: 100, createTime: '2023-11-30 16:45:00' }
        ]

        this.recentSynthesis = [
          { username: 'user001', text: '这是一个测试文本', modelName: 'zhexue_lao', status: 1 },
          { username: 'user002', text: '语音合成测试', modelName: 'xiaoshuo_nan', status: 1 },
          { username: 'user003', text: 'Hello World', modelName: 'zhexue_lao', status: 2 }
        ]

        this.updateCharts()
      }
    },

    updateCharts() {
      // 用户注册趋势图
      const dates = []
      const userData = []
      for (let i = 6; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        dates.push(date.toLocaleDateString())
        userData.push(Math.floor(Math.random() * 50) + 10)
      }
      
      this.userTrendOption.xAxis.data = dates
      this.userTrendOption.series[0].data = userData

      // 语音合成统计图
      const models = ['zhexue_lao', 'xiaoshuo_nan', 'default']
      const synthesisData = [450, 320, 180]
      
      this.synthesisOption.xAxis.data = models
      this.synthesisOption.series[0].data = synthesisData
    }
  },
  mounted() {
    this.loadDashboardData()
  }
}
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.user-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.synthesis-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.points-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.today-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content h3 {
  font-size: 28px;
  color: #333;
  margin: 0 0 5px 0;
}

.stat-content p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.charts-row {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
}

.activity-row {
  margin-bottom: 20px;
}
</style>
