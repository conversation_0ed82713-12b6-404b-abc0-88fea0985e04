package com.voice.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.voice.common.Result;
import com.voice.entity.PointRecord;
import com.voice.service.PointRecordService;
import com.voice.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 管理员积分管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/admin")
@CrossOrigin
public class AdminPointsController {

    @Autowired
    private PointRecordService pointRecordService;

    @Autowired
    private UserService userService;

    /**
     * 获取积分记录列表
     */
    @GetMapping("/point-records")
    public Result<Map<String, Object>> getPointRecords(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) Integer type,
            @RequestParam(required = false) String userId) {
        try {
            Page<PointRecord> pageParam = new Page<>(page, size);
            QueryWrapper<PointRecord> queryWrapper = new QueryWrapper<>();
            
            if (type != null) {
                queryWrapper.eq("type", type);
            }
            
            if (StringUtils.hasText(userId)) {
                queryWrapper.eq("user_id", userId);
            }
            
            queryWrapper.orderByDesc("create_time");
            
            IPage<PointRecord> recordPage = pointRecordService.page(pageParam, queryWrapper);
            
            Map<String, Object> data = new HashMap<>();
            data.put("records", recordPage.getRecords());
            data.put("total", recordPage.getTotal());
            data.put("pages", recordPage.getPages());
            data.put("current", recordPage.getCurrent());
            data.put("size", recordPage.getSize());
            
            return Result.success(data);
        } catch (Exception e) {
            log.error("获取积分记录失败", e);
            return Result.error("获取积分记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取积分统计信息
     */
    @GetMapping("/points/stats")
    public Result<Map<String, Object>> getPointsStats() {
        try {
            Map<String, Object> stats = pointRecordService.getPointsStats();
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取积分统计信息失败", e);
            return Result.error("获取积分统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 手动调整用户积分
     */
    @PostMapping("/points/adjust")
    public Result<String> adjustUserPoints(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.parseLong(request.get("userId").toString());
            String type = request.get("type").toString();
            Integer points = Integer.parseInt(request.get("points").toString());
            String description = request.get("description").toString();
            
            boolean success = userService.adjustPoints(userId, type, points, description);
            
            if (success) {
                return Result.success("积分调整成功");
            } else {
                return Result.error("积分调整失败");
            }
        } catch (Exception e) {
            log.error("调整用户积分失败", e);
            return Result.error("调整用户积分失败: " + e.getMessage());
        }
    }

    /**
     * 批量调整积分
     */
    @PostMapping("/points/batch-adjust")
    public Result<String> batchAdjustPoints(@RequestBody Map<String, Object> request) {
        try {
            // 这里可以实现批量调整积分的逻辑
            // 暂时返回成功
            return Result.success("批量调整积分成功");
        } catch (Exception e) {
            log.error("批量调整积分失败", e);
            return Result.error("批量调整积分失败: " + e.getMessage());
        }
    }

    /**
     * 获取积分配置
     */
    @GetMapping("/points/config")
    public Result<Map<String, Object>> getPointsConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("synthesisCost", 10); // 语音合成消耗积分
            config.put("registerPoints", 100); // 注册赠送积分
            config.put("dailySignPoints", 5); // 每日签到积分
            
            return Result.success(config);
        } catch (Exception e) {
            log.error("获取积分配置失败", e);
            return Result.error("获取积分配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新积分配置
     */
    @PutMapping("/points/config")
    public Result<String> updatePointsConfig(@RequestBody Map<String, Object> config) {
        try {
            // 这里可以实现更新积分配置的逻辑
            // 暂时返回成功
            return Result.success("积分配置更新成功");
        } catch (Exception e) {
            log.error("更新积分配置失败", e);
            return Result.error("更新积分配置失败: " + e.getMessage());
        }
    }

    /**
     * 导出积分记录
     */
    @GetMapping("/point-records/export")
    public Result<String> exportPointRecords(
            @RequestParam(required = false) Integer type,
            @RequestParam(required = false) String userId) {
        try {
            // 这里可以实现导出积分记录的逻辑
            // 暂时返回成功
            return Result.success("积分记录导出成功");
        } catch (Exception e) {
            log.error("导出积分记录失败", e);
            return Result.error("导出积分记录失败: " + e.getMessage());
        }
    }
}
