<template>
  <div class="api-test">
    <el-card>
      <div slot="header">
        <span>API连接测试</span>
      </div>
      
      <div class="test-section">
        <h3>环境变量</h3>
        <p><strong>NODE_ENV:</strong> {{ nodeEnv }}</p>
        <p><strong>VUE_APP_BASE_API:</strong> {{ baseApi }}</p>
        <p><strong>实际使用的API地址:</strong> {{ actualApiUrl }}</p>
      </div>

      <div class="test-section">
        <h3>API连接测试</h3>
        <el-button type="primary" @click="testLogin" :loading="loginLoading">
          测试管理员登录
        </el-button>
        <el-button type="success" @click="testUserList" :loading="userLoading">
          测试获取用户列表
        </el-button>
        <el-button type="info" @click="testDashboard" :loading="dashboardLoading">
          测试仪表板数据
        </el-button>
      </div>

      <div class="test-section" v-if="testResults.length > 0">
        <h3>测试结果</h3>
        <div v-for="(result, index) in testResults" :key="index" class="test-result">
          <el-tag :type="result.success ? 'success' : 'danger'">
            {{ result.success ? '成功' : '失败' }}
          </el-tag>
          <span class="result-text">{{ result.message }}</span>
          <div v-if="result.data" class="result-data">
            <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { adminLogin } from '@/api/auth'
import { getUserList } from '@/api/user'
import { getDashboardStats } from '@/api/dashboard'

export default {
  name: 'ApiTest',
  data() {
    return {
      nodeEnv: process.env.NODE_ENV,
      baseApi: process.env.VUE_APP_BASE_API,
      actualApiUrl: '',
      loginLoading: false,
      userLoading: false,
      dashboardLoading: false,
      testResults: []
    }
  },
  mounted() {
    // 获取实际使用的API地址
    this.actualApiUrl = this.$http.defaults.baseURL
  },
  methods: {
    async testLogin() {
      this.loginLoading = true
      try {
        const response = await adminLogin({
          username: 'admin',
          password: 'admin123'
        })
        
        this.addTestResult({
          success: true,
          message: '管理员登录测试成功',
          data: {
            token: response.data.token ? response.data.token.substring(0, 20) + '...' : null,
            admin: response.data.admin
          }
        })
      } catch (error) {
        this.addTestResult({
          success: false,
          message: '管理员登录测试失败: ' + error.message,
          data: error.response ? error.response.data : null
        })
      } finally {
        this.loginLoading = false
      }
    },

    async testUserList() {
      this.userLoading = true
      try {
        const response = await getUserList({
          page: 1,
          size: 5
        })
        
        this.addTestResult({
          success: true,
          message: '获取用户列表测试成功',
          data: {
            total: response.data.total,
            recordCount: response.data.records ? response.data.records.length : 0
          }
        })
      } catch (error) {
        this.addTestResult({
          success: false,
          message: '获取用户列表测试失败: ' + error.message,
          data: error.response ? error.response.data : null
        })
      } finally {
        this.userLoading = false
      }
    },

    async testDashboard() {
      this.dashboardLoading = true
      try {
        const response = await getDashboardStats()
        
        this.addTestResult({
          success: true,
          message: '获取仪表板数据测试成功',
          data: response.data
        })
      } catch (error) {
        this.addTestResult({
          success: false,
          message: '获取仪表板数据测试失败: ' + error.message,
          data: error.response ? error.response.data : null
        })
      } finally {
        this.dashboardLoading = false
      }
    },

    addTestResult(result) {
      this.testResults.unshift({
        ...result,
        timestamp: new Date().toLocaleString()
      })
      
      // 只保留最近10条结果
      if (this.testResults.length > 10) {
        this.testResults = this.testResults.slice(0, 10)
      }
    }
  }
}
</script>

<style scoped>
.api-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
}

.test-section h3 {
  margin-bottom: 15px;
  color: #333;
}

.test-section p {
  margin: 5px 0;
  font-family: monospace;
}

.test-result {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
}

.result-text {
  margin-left: 10px;
  font-weight: bold;
}

.result-data {
  margin-top: 10px;
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.result-data pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
