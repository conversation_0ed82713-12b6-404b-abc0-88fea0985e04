{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;AAAA,2CAKqB;AAIrB,SAAgB,kBAAkB,CAAC,KAAa,EAAE,KAAa;IAC7D,IAAI,KAAK,KAAK,CAAC;QAAE,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC;IAC5C,OAAO,iCAAmB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC3C,CAAC;AAHD,gDAGC;AAED,SAAgB,uBAAuB,CAAC,KAAa,EAAE,KAAa;IAClE,IAAI,KAAK,KAAK,CAAC;QAAE,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC;IAC5C,OAAO,sCAAwB,CAAC,KAAK,CAAC,CAAC;AACzC,CAAC;AAHD,0DAGC;AAED,SAAgB,SAAS,CAAC,KAAa,EAAE,OAAqB;IAArB,wBAAA,EAAA,YAAqB;IAC5D,OAAO,wBAAU,CAAC,KAAK,qBACrB,SAAS,EAAE,kBAAkB,IAC1B,OAAO,EACV,CAAC;AACL,CAAC;AALD,8BAKC", "sourcesContent": ["import {\n  pascalCase,\n  pascalCaseTransform,\n  pascalCaseTransformMerge,\n  Options,\n} from \"pascal-case\";\n\nexport { Options };\n\nexport function camelCaseTransform(input: string, index: number) {\n  if (index === 0) return input.toLowerCase();\n  return pascalCaseTransform(input, index);\n}\n\nexport function camelCaseTransformMerge(input: string, index: number) {\n  if (index === 0) return input.toLowerCase();\n  return pascalCaseTransformMerge(input);\n}\n\nexport function camelCase(input: string, options: Options = {}) {\n  return pascalCase(input, {\n    transform: camelCaseTransform,\n    ...options,\n  });\n}\n"]}