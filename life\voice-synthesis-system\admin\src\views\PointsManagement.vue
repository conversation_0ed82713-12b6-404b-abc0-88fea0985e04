<template>
  <div class="points-management">
    <el-card>
      <div slot="header" class="card-header">
        <span>积分记录管理</span>
        <div class="header-actions">
          <el-select v-model="filterType" placeholder="类型筛选" style="width: 120px; margin-right: 10px;">
            <el-option label="全部" value=""></el-option>
            <el-option label="获得" :value="1"></el-option>
            <el-option label="消耗" :value="2"></el-option>
          </el-select>
          <el-input
            v-model="searchUserId"
            placeholder="用户ID"
            style="width: 120px; margin-right: 10px;">
          </el-input>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </div>
      </div>

      <!-- 积分记录列表 -->
      <el-table :data="records" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="userId" label="用户ID" width="100"></el-table-column>
        <el-table-column prop="username" label="用户名" width="120"></el-table-column>
        <el-table-column prop="type" label="类型" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.type === 1 ? 'success' : 'warning'" size="mini">
              {{ scope.row.type === 1 ? '获得' : '消耗' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="points" label="积分数量" width="100">
          <template slot-scope="scope">
            <span :style="{ color: scope.row.type === 1 ? '#67C23A' : '#E6A23C' }">
              {{ scope.row.type === 1 ? '+' : '-' }}{{ scope.row.points }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="source" label="来源" width="120"></el-table-column>
        <el-table-column prop="description" label="描述" show-overflow-tooltip></el-table-column>
        <el-table-column prop="beforePoints" label="操作前" width="100"></el-table-column>
        <el-table-column prop="afterPoints" label="操作后" width="100"></el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180"></el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </el-card>

    <!-- 统计信息 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="8">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon points-icon">
              <i class="el-icon-coin"></i>
            </div>
            <div class="stat-content">
              <h3>{{ stats.totalPoints }}</h3>
              <p>总积分消耗</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon gain-icon">
              <i class="el-icon-plus"></i>
            </div>
            <div class="stat-content">
              <h3>{{ stats.totalGain }}</h3>
              <p>总积分获得</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon today-icon">
              <i class="el-icon-date"></i>
            </div>
            <div class="stat-content">
              <h3>{{ stats.todayConsume }}</h3>
              <p>今日消耗</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getPointRecords, getPointStats } from '@/api/points'

export default {
  name: 'PointsManagement',
  data() {
    return {
      records: [],
      loading: false,
      filterType: '',
      searchUserId: '',
      currentPage: 1,
      pageSize: 20,
      total: 0,
      stats: {
        totalPoints: 0,
        totalGain: 0,
        todayConsume: 0
      }
    }
  },
  methods: {
    async loadRecords() {
      this.loading = true
      try {
        const response = await getPointRecords({
          page: this.currentPage,
          size: this.pageSize,
          type: this.filterType,
          userId: this.searchUserId
        })
        this.records = response.data.records || []
        this.total = response.data.total || 0
      } catch (error) {
        console.error('加载积分记录失败:', error)
        this.$message.error('加载积分记录失败')
        // 如果API调用失败，使用模拟数据作为后备
        this.records = [
          {
            id: 1,
            userId: 1,
            username: 'user001',
            type: 1,
            points: 100,
            source: 'register',
            description: '注册赠送积分',
            beforePoints: 0,
            afterPoints: 100,
            createTime: '2023-12-01 10:00:00'
          },
          {
            id: 2,
            userId: 1,
            username: 'user001',
            type: 2,
            points: 10,
            source: 'synthesis',
            description: '语音合成消耗积分',
            beforePoints: 100,
            afterPoints: 90,
            createTime: '2023-12-01 10:30:00'
          },
          {
            id: 3,
            userId: 2,
            username: 'user002',
            type: 1,
            points: 100,
            source: 'register',
            description: '注册赠送积分',
            beforePoints: 0,
            afterPoints: 100,
            createTime: '2023-12-01 09:00:00'
          }
        ]
        this.total = 3
      } finally {
        this.loading = false
      }
    },

    async loadStats() {
      try {
        const response = await getPointStats()
        this.stats = response.data || {
          totalPoints: 0,
          totalGain: 0,
          todayConsume: 0
        }
      } catch (error) {
        console.error('加载积分统计失败:', error)
        this.$message.error('加载积分统计失败')
        // 如果API调用失败，使用模拟数据作为后备
        this.stats = {
          totalPoints: 8640,
          totalGain: 12500,
          todayConsume: 150
        }
      }
    },

    handleSearch() {
      this.currentPage = 1
      this.loadRecords()
    },

    resetSearch() {
      this.filterType = ''
      this.searchUserId = ''
      this.currentPage = 1
      this.loadRecords()
    },

    handleSizeChange(val) {
      this.pageSize = val
      this.loadRecords()
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.loadRecords()
    }
  },
  mounted() {
    this.loadRecords()
    this.loadStats()
  }
}
</script>

<style scoped>
.points-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.stat-card {
  text-align: center;
}

.stat-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.points-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gain-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.today-icon {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-content h3 {
  font-size: 28px;
  color: #333;
  margin: 0 0 5px 0;
}

.stat-content p {
  color: #666;
  margin: 0;
  font-size: 14px;
}
</style>
