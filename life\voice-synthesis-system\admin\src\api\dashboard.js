import request from './request'

// 获取仪表板统计数据
export function getDashboardStats() {
  return request({
    url: '/admin/dashboard/stats',
    method: 'get'
  })
}

// 获取用户增长趋势
export function getUserGrowthTrend(params) {
  return request({
    url: '/admin/dashboard/user-growth',
    method: 'get',
    params
  })
}

// 获取合成使用趋势
export function getSynthesisUsageTrend(params) {
  return request({
    url: '/admin/dashboard/synthesis-usage',
    method: 'get',
    params
  })
}

// 获取积分消耗趋势
export function getPointsConsumptionTrend(params) {
  return request({
    url: '/admin/dashboard/points-consumption',
    method: 'get',
    params
  })
}

// 获取热门语音模型
export function getPopularVoiceModels() {
  return request({
    url: '/admin/dashboard/popular-models',
    method: 'get'
  })
}

// 获取最近活动
export function getRecentActivities(params) {
  return request({
    url: '/admin/dashboard/recent-activities',
    method: 'get',
    params
  })
}

// 获取系统概览
export function getSystemOverview() {
  return request({
    url: '/admin/dashboard/system-overview',
    method: 'get'
  })
}
