<template>
  <div class="layout-container">
    <!-- 科技感背景 -->
    <div class="tech-background">
      <div class="bg-particles"></div>
      <div class="bg-grid"></div>
    </div>

    <!-- 科技感顶部导航栏 -->
    <div class="tech-header">
      <div class="header-background"></div>
      <div class="header-content">
        <!-- Logo和标题 -->
        <div class="logo-section">
          <div class="logo-icon">
            <i class="el-icon-microphone"></i>
          </div>
          <h1 class="system-title">语音合成系统</h1>
        </div>

        <!-- 导航菜单 -->
        <div class="nav-menu">
          <div
            class="nav-item"
            :class="{ active: $route.path === '/layout/home' }"
            @click="$router.push('/layout/home')"
          >
            <i class="el-icon-house"></i>
            <span>首页</span>
          </div>
          <div
            class="nav-item"
            :class="{ active: $route.path === '/layout/synthesis' }"
            @click="$router.push('/layout/synthesis')"
          >
            <i class="el-icon-microphone"></i>
            <span>语音合成</span>
          </div>
          <div
            class="nav-item"
            :class="{ active: $route.path === '/layout/records' }"
            @click="$router.push('/layout/records')"
          >
            <i class="el-icon-document"></i>
            <span>合成记录</span>
          </div>
          <div
            class="nav-item"
            :class="{ active: $route.path === '/layout/profile' }"
            @click="$router.push('/layout/profile')"
          >
            <i class="el-icon-user"></i>
            <span>个人中心</span>
          </div>
        </div>

        <!-- 用户信息区域 -->
        <div class="user-section">
          <!-- 主题切换按钮 -->
          <div class="theme-toggle" @click="toggleTheme">
            <div class="theme-toggle-track" :class="{ active: isDarkTheme }">
              <div class="theme-toggle-thumb">
                <i class="theme-icon" :class="isDarkTheme ? 'el-icon-moon' : 'el-icon-sunny'"></i>
              </div>
            </div>
            <span class="theme-label">{{ isDarkTheme ? '夜间' : '白天' }}</span>
          </div>

          <div class="points-display">
            <div class="points-icon">
              <i class="el-icon-coin"></i>
            </div>
            <div class="points-info">
              <span class="points-value">{{ userPoints }}</span>
              <span class="points-label">积分</span>
            </div>
          </div>
          <el-dropdown @command="handleCommand" class="user-dropdown">
            <div class="user-avatar">
              <span class="username">{{ currentUser.nickname || currentUser.username }}</span>
              <i class="el-icon-arrow-down"></i>
            </div>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="profile">个人中心</el-dropdown-item>
              <el-dropdown-item command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>

          <!-- 移动端菜单按钮 -->
          <button class="mobile-menu-btn" @click="showMobileMenu = !showMobileMenu">
            <i class="el-icon-menu"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 移动端菜单 -->
    <div v-if="showMobileMenu" class="mobile-menu-overlay" @click="showMobileMenu = false">
      <div class="mobile-menu" @click.stop>
        <div class="mobile-menu-header">
          <h3>导航菜单</h3>
          <button class="close-btn" @click="showMobileMenu = false">
            <i class="el-icon-close"></i>
          </button>
        </div>
        <div class="mobile-menu-items">
          <div
            class="mobile-nav-item"
            :class="{ active: $route.path === '/layout/home' }"
            @click="navigateAndClose('/layout/home')"
          >
            <i class="el-icon-house"></i>
            <span>首页</span>
          </div>
          <div
            class="mobile-nav-item"
            :class="{ active: $route.path === '/layout/synthesis' }"
            @click="navigateAndClose('/layout/synthesis')"
          >
            <i class="el-icon-microphone"></i>
            <span>语音合成</span>
          </div>
          <div
            class="mobile-nav-item"
            :class="{ active: $route.path === '/layout/records' }"
            @click="navigateAndClose('/layout/records')"
          >
            <i class="el-icon-document"></i>
            <span>合成记录</span>
          </div>
          <div
            class="mobile-nav-item"
            :class="{ active: $route.path === '/layout/profile' }"
            @click="navigateAndClose('/layout/profile')"
          >
            <i class="el-icon-user"></i>
            <span>个人中心</span>
          </div>
          <div class="mobile-theme-toggle" @click="toggleTheme">
            <i :class="isDarkTheme ? 'el-icon-moon' : 'el-icon-sunny'"></i>
            <span>{{ isDarkTheme ? '切换到白天模式' : '切换到夜间模式' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-container">
      <router-view/>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Layout',
  data() {
    return {
      showMobileMenu: false
    }
  },
  computed: {
    ...mapGetters(['currentUser', 'userPoints', 'currentTheme', 'isDarkTheme', 'isLightTheme'])
  },
  mounted() {
    // 初始化主题
    this.$store.dispatch('setTheme', this.currentTheme)
  },
  methods: {
    handleCommand(command) {
      if (command === 'logout') {
        this.$store.dispatch('logout')
        this.$router.push('/login')
      } else if (command === 'profile') {
        this.$router.push('/layout/profile')
      }
    },
    navigateAndClose(path) {
      this.$router.push(path)
      this.showMobileMenu = false
    },
    toggleTheme() {
      this.$store.dispatch('toggleTheme')
    }
  }
}
</script>

<style scoped>
/* 全局容器 */
.layout-container {
  min-height: 100vh;
  background: var(--bg-primary);
  position: relative;
  overflow-x: hidden;
}

/* 科技感背景 */
.tech-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.bg-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, var(--particle-color), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(103, 194, 58, 0.3), transparent),
    radial-gradient(1px 1px at 90px 40px, var(--particle-color), transparent);
  background-repeat: repeat;
  background-size: 200px 200px;
  animation: particleFloat 20s linear infinite;
}

@keyframes particleFloat {
  0% { transform: translate(0, 0); }
  100% { transform: translate(-200px, -200px); }
}

.bg-grid {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(var(--grid-color) 1px, transparent 1px),
    linear-gradient(90deg, var(--grid-color) 1px, transparent 1px);
  background-size: 50px 50px;
  opacity: 0.3;
}

/* 科技感顶部导航栏 */
.tech-header {
  position: relative;
  height: 80px;
  background: var(--bg-header);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-accent);
  box-shadow: 0 4px 20px var(--shadow-primary);
  z-index: 100;
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, 
    rgba(64, 158, 255, 0.1) 0%, 
    rgba(64, 158, 255, 0.05) 50%, 
    rgba(64, 158, 255, 0.1) 100%);
  animation: headerGlow 3s ease-in-out infinite alternate;
}

@keyframes headerGlow {
  0% { opacity: 0.5; }
  100% { opacity: 1; }
}

.header-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 40px;
  z-index: 10;
}

/* Logo区域 */
.logo-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

.logo-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);
}

.logo-icon i {
  font-size: 24px;
  color: white;
}

.system-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  text-shadow: 0 2px 10px var(--shadow-accent);
  letter-spacing: 1px;
}

/* 导航菜单 */
.nav-menu {
  display: flex;
  gap: 30px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 25px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  font-weight: 500;
}

.nav-item:hover {
  color: var(--text-accent);
  background: var(--bg-tertiary);
  transform: translateY(-2px);
}

.nav-item.active {
  color: var(--text-accent);
  background: var(--bg-tertiary);
  box-shadow: 0 4px 15px var(--shadow-accent);
}

.nav-item i {
  font-size: 18px;
}

/* 用户信息区域 */
.user-section {
  display: flex;
  align-items: center;
  gap: 25px;
}

/* 主题切换按钮 */
.theme-toggle {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.theme-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
}

.theme-toggle-track {
  position: relative;
  width: 50px;
  height: 26px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 13px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.theme-toggle-track.active {
  background: linear-gradient(135deg, #1a1a2e, #16213e);
  border-color: rgba(64, 158, 255, 0.5);
}

.theme-toggle-thumb {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.theme-toggle-track.active .theme-toggle-thumb {
  transform: translateX(24px);
  background: linear-gradient(135deg, #4A90E2, #357ABD);
}

.theme-icon {
  font-size: 12px;
  color: white;
  transition: all 0.3s ease;
}

.theme-label {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
  min-width: 30px;
  transition: color 0.3s ease;
}

.points-display {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 20px;
  background: var(--bg-tertiary);
  border-radius: 20px;
  border: 1px solid var(--border-accent);
}

.points-icon {
  width: 35px;
  height: 35px;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: coinSpin 2s linear infinite;
}

@keyframes coinSpin {
  0% { transform: rotateY(0deg); }
  100% { transform: rotateY(360deg); }
}

.points-icon i {
  color: white;
  font-size: 16px;
}

.points-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.points-value {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-accent);
}

.points-label {
  font-size: 12px;
  color: var(--text-tertiary);
}

.user-dropdown {
  cursor: pointer;
}

.user-avatar {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 15px;
  background: var(--bg-secondary);
  border-radius: 20px;
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.user-avatar:hover {
  background: var(--bg-tertiary);
  transform: translateY(-1px);
}

.username {
  font-weight: 500;
}

/* 主内容区域 */
.main-container {
  position: relative;
  z-index: 10;
  min-height: calc(100vh - 80px);
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
  display: none;
  width: 40px;
  height: 40px;
  background: rgba(64, 158, 255, 0.2);
  border: 1px solid rgba(64, 158, 255, 0.3);
  border-radius: 8px;
  color: #409EFF;
  cursor: pointer;
  transition: all 0.3s ease;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.mobile-menu-btn:hover {
  background: rgba(64, 158, 255, 0.3);
}

/* 移动端菜单覆盖层 */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 1000;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding-top: 80px;
  animation: overlayFadeIn 0.3s ease;
}

@keyframes overlayFadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

.mobile-menu {
  background: var(--bg-card);
  backdrop-filter: blur(20px);
  border-radius: 20px 0 0 20px;
  border: 1px solid var(--border-primary);
  width: 280px;
  max-width: 80vw;
  height: calc(100vh - 80px);
  animation: menuSlideIn 0.3s ease;
}

@keyframes menuSlideIn {
  0% { transform: translateX(100%); }
  100% { transform: translateX(0); }
}

.mobile-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-menu-header h3 {
  color: var(--text-primary);
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  width: 30px;
  height: 30px;
  background: rgba(245, 108, 108, 0.2);
  border: 1px solid rgba(245, 108, 108, 0.3);
  border-radius: 50%;
  color: #F56C6C;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.close-btn:hover {
  background: rgba(245, 108, 108, 0.3);
}

.mobile-menu-items {
  padding: 20px;
}

.mobile-nav-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  margin-bottom: 10px;
  border-radius: 12px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.mobile-nav-item:hover {
  color: var(--text-accent);
  background: var(--bg-tertiary);
}

.mobile-nav-item.active {
  color: var(--text-accent);
  background: var(--bg-tertiary);
  box-shadow: 0 4px 15px var(--shadow-accent);
}

.mobile-nav-item i {
  font-size: 18px;
  width: 20px;
}

.mobile-theme-toggle {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  margin-top: 20px;
  border-top: 1px solid var(--border-primary);
  border-radius: 12px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.mobile-theme-toggle:hover {
  color: var(--text-accent);
  background: var(--bg-tertiary);
}

.mobile-theme-toggle i {
  font-size: 18px;
  width: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 20px;
  }

  .nav-menu {
    display: none;
  }

  .mobile-menu-btn {
    display: flex;
  }

  .system-title {
    font-size: 20px;
  }

  .user-section {
    gap: 15px;
  }

  .points-display {
    padding: 8px 15px;
  }
}
</style>
