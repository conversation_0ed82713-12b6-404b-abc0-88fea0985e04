const axios = require('axios');

// 配置基础URL
const BASE_URL = 'http://localhost:8080/api';

// 测试管理员API
async function testAdminAPI() {
    try {
        console.log('开始测试管理员API...\n');

        // 1. 管理员登录
        console.log('1. 测试管理员登录...');
        const loginResponse = await axios.post(`${BASE_URL}/admin/login`, {
            username: 'admin',
            password: 'admin123'
        });
        
        if (loginResponse.data.code === 200) {
            console.log('✓ 管理员登录成功');
            const token = loginResponse.data.data.token;
            console.log('Token:', token.substring(0, 20) + '...\n');

            // 设置请求头
            const headers = {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            };

            // 2. 获取管理员信息
            console.log('2. 测试获取管理员信息...');
            const adminInfoResponse = await axios.get(`${BASE_URL}/admin/info`, { headers });
            if (adminInfoResponse.data.code === 200) {
                console.log('✓ 获取管理员信息成功');
                console.log('管理员:', adminInfoResponse.data.data.username, '\n');
            }

            // 3. 获取用户列表
            console.log('3. 测试获取用户列表...');
            const usersResponse = await axios.get(`${BASE_URL}/admin/users?page=1&size=10`, { headers });
            if (usersResponse.data.code === 200) {
                console.log('✓ 获取用户列表成功');
                console.log('用户总数:', usersResponse.data.data.total, '\n');
            }

            // 4. 获取积分记录
            console.log('4. 测试获取积分记录...');
            const pointsResponse = await axios.get(`${BASE_URL}/admin/point-records?page=1&size=10`, { headers });
            if (pointsResponse.data.code === 200) {
                console.log('✓ 获取积分记录成功');
                console.log('积分记录总数:', pointsResponse.data.data.total, '\n');
            }

            // 5. 获取合成记录
            console.log('5. 测试获取合成记录...');
            const synthesisResponse = await axios.get(`${BASE_URL}/admin/synthesis-records?page=1&size=10`, { headers });
            if (synthesisResponse.data.code === 200) {
                console.log('✓ 获取合成记录成功');
                console.log('合成记录总数:', synthesisResponse.data.data.total, '\n');
            }

            // 6. 获取仪表板统计
            console.log('6. 测试获取仪表板统计...');
            const dashboardResponse = await axios.get(`${BASE_URL}/admin/dashboard/stats`, { headers });
            if (dashboardResponse.data.code === 200) {
                console.log('✓ 获取仪表板统计成功');
                console.log('统计数据:', dashboardResponse.data.data, '\n');
            }

            // 7. 获取系统配置
            console.log('7. 测试获取系统配置...');
            const configResponse = await axios.get(`${BASE_URL}/admin/system/config`, { headers });
            if (configResponse.data.code === 200) {
                console.log('✓ 获取系统配置成功');
                console.log('系统配置:', configResponse.data.data, '\n');
            }

            console.log('所有API测试完成！');

        } else {
            console.log('✗ 管理员登录失败:', loginResponse.data.message);
        }

    } catch (error) {
        console.error('测试过程中出现错误:');
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('错误信息:', error.response.data);
        } else {
            console.error('错误:', error.message);
        }
    }
}

// 运行测试
testAdminAPI();
