/**
 * 认证相关API
 */
import request from './index'
import { API_ENDPOINTS } from './config'

// 用户登录
export function login(data) {
  return request({
    url: API_ENDPOINTS.AUTH.LOGIN,
    method: 'post',
    data
  })
}

// 用户注册
export function register(data) {
  return request({
    url: API_ENDPOINTS.AUTH.REGISTER,
    method: 'post',
    data
  })
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: API_ENDPOINTS.AUTH.USER_INFO,
    method: 'get'
  })
}

// 刷新token
export function refreshToken() {
  return request({
    url: API_ENDPOINTS.AUTH.REFRESH,
    method: 'post'
  })
}

// 用户登出
export function logout() {
  return request({
    url: API_ENDPOINTS.AUTH.LOGOUT,
    method: 'post'
  })
}
