package com.voice.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voice.entity.Admin;
import com.voice.mapper.AdminMapper;
import com.voice.service.AdminService;
import com.voice.util.PasswordUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 管理员服务实现类
 */
@Slf4j
@Service
public class AdminServiceImpl extends ServiceImpl<AdminMapper, Admin> implements AdminService {

    @Autowired
    private PasswordUtil passwordUtil;

    @Override
    public Admin login(String username, String password) {
        try {
            QueryWrapper<Admin> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("username", username);
            queryWrapper.eq("status", 1); // 只查询启用状态的管理员
            
            Admin admin = this.getOne(queryWrapper);
            if (admin == null) {
                return null;
            }
            
            // 验证密码
            if (!passwordUtil.matches(password, admin.getPassword())) {
                return null;
            }
            
            // 更新最后登录时间
            admin.setLastLoginTime(LocalDateTime.now());
            this.updateById(admin);
            
            return admin;
        } catch (Exception e) {
            log.error("管理员登录失败", e);
            return null;
        }
    }

    @Override
    public boolean changePassword(Long adminId, String oldPassword, String newPassword) {
        try {
            Admin admin = this.getById(adminId);
            if (admin == null) {
                return false;
            }
            
            // 验证旧密码
            if (!passwordUtil.matches(oldPassword, admin.getPassword())) {
                return false;
            }
            
            // 设置新密码
            admin.setPassword(passwordUtil.encode(newPassword));
            return this.updateById(admin);
        } catch (Exception e) {
            log.error("修改密码失败", e);
            return false;
        }
    }
}
