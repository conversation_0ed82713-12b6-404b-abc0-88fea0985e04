package com.voice.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voice.dto.LoginRequest;
import com.voice.dto.RegisterRequest;
import com.voice.entity.PointRecord;
import com.voice.entity.User;
import com.voice.mapper.UserMapper;
import com.voice.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户服务类
 */
@Service
public class UserService extends ServiceImpl<UserMapper, User> {

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private PointRecordService pointRecordService;

    /**
     * 用户注册
     */
    @Transactional
    public Map<String, Object> register(RegisterRequest request) {
        // 检查用户名是否已存在
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.eq("username", request.getUsername());
        if (this.count(wrapper) > 0) {
            throw new RuntimeException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (request.getEmail() != null && !request.getEmail().isEmpty()) {
            wrapper = new QueryWrapper<>();
            wrapper.eq("email", request.getEmail());
            if (this.count(wrapper) > 0) {
                throw new RuntimeException("邮箱已被注册");
            }
        }

        // 创建用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setEmail(request.getEmail());
        user.setPhone(request.getPhone());
        user.setNickname(request.getNickname() != null ? request.getNickname() : request.getUsername());
        user.setPoints(100); // 注册赠送100积分
        user.setStatus(1);
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());

        this.save(user);

        // 记录积分获得记录
        pointRecordService.addPointRecord(user.getId(), 1, 100, "register", "注册赠送积分", 0, 100);

        // 生成token
        String token = jwtUtil.generateToken(user.getUsername(), "user", user.getId());

        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("user", user);
        return result;
    }

    /**
     * 用户登录
     */
    public Map<String, Object> login(LoginRequest request) {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.eq("username", request.getUsername());
        User user = this.getOne(wrapper);

        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        if (user.getStatus() == 0) {
            throw new RuntimeException("账号已被禁用");
        }

        if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
            throw new RuntimeException("密码错误");
        }

        // 生成token
        String token = jwtUtil.generateToken(user.getUsername(), "user", user.getId());

        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("user", user);
        return result;
    }

    /**
     * 根据用户名获取用户
     */
    public User getUserByUsername(String username) {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.eq("username", username);
        return this.getOne(wrapper);
    }

    /**
     * 扣除用户积分
     */
    @Transactional
    public boolean deductPoints(Long userId, Integer points, String source, String description) {
        User user = this.getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        if (user.getPoints() < points) {
            throw new RuntimeException("积分不足");
        }

        int beforePoints = user.getPoints();
        int afterPoints = beforePoints - points;

        // 更新用户积分
        user.setPoints(afterPoints);
        this.updateById(user);

        // 记录积分消耗记录
        pointRecordService.addPointRecord(userId, 2, points, source, description, beforePoints, afterPoints);

        return true;
    }

    /**
     * 增加用户积分
     */
    @Transactional
    public boolean addPoints(Long userId, Integer points, String source, String description) {
        User user = this.getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        int beforePoints = user.getPoints();
        int afterPoints = beforePoints + points;

        // 更新用户积分
        user.setPoints(afterPoints);
        this.updateById(user);

        // 记录积分获得记录
        pointRecordService.addPointRecord(userId, 1, points, source, description, beforePoints, afterPoints);

        return true;
    }

    /**
     * 分页查询用户
     */
    public IPage<User> getUserPage(int current, int size, String keyword) {
        Page<User> page = new Page<>(current, size);
        QueryWrapper<User> wrapper = new QueryWrapper<>();

        if (keyword != null && !keyword.isEmpty()) {
            wrapper.like("username", keyword)
                   .or().like("nickname", keyword)
                   .or().like("email", keyword);
        }

        wrapper.orderByDesc("create_time");
        return this.page(page, wrapper);
    }

    /**
     * 重置用户密码
     */
    public boolean resetPassword(Long userId, String newPassword) {
        User user = this.getById(userId);
        if (user == null) {
            return false;
        }

        user.setPassword(passwordEncoder.encode(newPassword));
        return this.updateById(user);
    }

    /**
     * 调整用户积分（管理员功能）
     */
    @Transactional
    public boolean adjustPoints(Long userId, String type, Integer points, String description) {
        if ("add".equals(type)) {
            return addPoints(userId, points, "admin_adjust", description);
        } else if ("deduct".equals(type)) {
            return deductPoints(userId, points, "admin_adjust", description);
        }
        return false;
    }

    /**
     * 获取用户统计信息
     */
    public Map<String, Object> getUserStats() {
        Map<String, Object> stats = new HashMap<>();

        // 总用户数
        long totalUsers = this.count();
        stats.put("totalUsers", totalUsers);

        // 今日新增用户数
        QueryWrapper<User> todayWrapper = new QueryWrapper<>();
        todayWrapper.ge("create_time", LocalDateTime.now().toLocalDate());
        long todayUsers = this.count(todayWrapper);
        stats.put("todayUsers", todayUsers);

        // 活跃用户数（最近7天登录）
        // 这里需要根据实际的登录记录表来查询，暂时使用模拟数据
        stats.put("activeUsers", totalUsers * 0.6);

        // 禁用用户数
        QueryWrapper<User> disabledWrapper = new QueryWrapper<>();
        disabledWrapper.eq("status", 0);
        long disabledUsers = this.count(disabledWrapper);
        stats.put("disabledUsers", disabledUsers);

        return stats;
    }
}
