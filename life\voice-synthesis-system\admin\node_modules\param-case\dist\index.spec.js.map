{"version": 3, "file": "index.spec.js", "sourceRoot": "", "sources": ["../src/index.spec.ts"], "names": [], "mappings": ";;AAAA,sBAA8B;AAE9B,IAAM,UAAU,GAAuB;IACrC,CAAC,EAAE,EAAE,EAAE,CAAC;IACR,CAAC,MAAM,EAAE,MAAM,CAAC;IAChB,CAAC,aAAa,EAAE,aAAa,CAAC;IAC9B,CAAC,aAAa,EAAE,aAAa,CAAC;IAC9B,CAAC,QAAQ,EAAE,SAAS,CAAC;IACrB,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IACpC,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;CACrC,CAAC;AAEF,QAAQ,CAAC,YAAY,EAAE;4BACT,KAAK,EAAE,MAAM;QACvB,EAAE,CAAI,KAAK,YAAO,MAAQ,EAAE;YAC1B,MAAM,CAAC,YAAS,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;;IAHL,KAA8B,UAAU,EAAV,yBAAU,EAAV,wBAAU,EAAV,IAAU;QAA7B,IAAA,qBAAe,EAAd,KAAK,QAAA,EAAE,MAAM,QAAA;gBAAb,KAAK,EAAE,MAAM;KAIxB;AACH,CAAC,CAAC,CAAC", "sourcesContent": ["import { paramCase } from \".\";\n\nconst TEST_CASES: [string, string][] = [\n  [\"\", \"\"],\n  [\"test\", \"test\"],\n  [\"test string\", \"test-string\"],\n  [\"Test String\", \"test-string\"],\n  [\"TestV2\", \"test-v2\"],\n  [\"version 1.2.10\", \"version-1-2-10\"],\n  [\"version 1.21.0\", \"version-1-21-0\"],\n];\n\ndescribe(\"param case\", () => {\n  for (const [input, result] of TEST_CASES) {\n    it(`${input} -> ${result}`, () => {\n      expect(paramCase(input)).toEqual(result);\n    });\n  }\n});\n"]}