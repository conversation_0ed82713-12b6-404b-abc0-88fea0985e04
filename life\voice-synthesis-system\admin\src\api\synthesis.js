import request from './request'

// 获取合成记录列表
export function getSynthesisRecords(params) {
  return request({
    url: '/admin/synthesis-records',
    method: 'get',
    params
  })
}

// 获取合成统计信息
export function getSynthesisStats() {
  return request({
    url: '/admin/synthesis/stats',
    method: 'get'
  })
}

// 获取合成记录详情
export function getSynthesisDetail(id) {
  return request({
    url: `/admin/synthesis-records/${id}`,
    method: 'get'
  })
}

// 删除合成记录
export function deleteSynthesisRecord(id) {
  return request({
    url: `/admin/synthesis-records/${id}`,
    method: 'delete'
  })
}

// 批量删除合成记录
export function batchDeleteSynthesisRecords(ids) {
  return request({
    url: '/admin/synthesis-records/batch-delete',
    method: 'post',
    data: { ids }
  })
}

// 获取语音模型列表
export function getVoiceModels() {
  return request({
    url: '/admin/voice-models',
    method: 'get'
  })
}

// 创建语音模型
export function createVoiceModel(data) {
  return request({
    url: '/admin/voice-models',
    method: 'post',
    data
  })
}

// 更新语音模型
export function updateVoiceModel(id, data) {
  return request({
    url: `/admin/voice-models/${id}`,
    method: 'put',
    data
  })
}

// 删除语音模型
export function deleteVoiceModel(id) {
  return request({
    url: `/admin/voice-models/${id}`,
    method: 'delete'
  })
}

// 导出合成记录
export function exportSynthesisRecords(params) {
  return request({
    url: '/admin/synthesis-records/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
