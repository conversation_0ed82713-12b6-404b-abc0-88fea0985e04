import Vue from 'vue'
import App from './App'
import router from './router'
import store from './store'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import './styles/themes.css'
import api from './api/api'
import request from './api/index'

Vue.config.productionTip = false

// 使用Element UI
Vue.use(ElementUI)

// 将API挂载到Vue原型上
Vue.prototype.$api = api
Vue.prototype.$http = request

// 初始化主题
const theme = localStorage.getItem('theme') || 'dark'
document.documentElement.className = `theme-${theme}`

new Vue({
  el: '#app',
  router,
  store,
  components: { App },
  template: '<App/>'
})
